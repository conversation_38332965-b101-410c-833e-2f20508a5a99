# Gift Card Transaction Discrepancy Analysis & Fix Solution

## Overview

This solution addresses discrepancies between the POS system and database gift card transactions where some transactions show `ChargeAmount = 0` in POS but have actual amounts in the database.

## Problem Analysis

- **POS System**: Shows `ChargeAmount = 0` with successful response (`ResponseCode = 200`)
- **Database**: Shows actual charge amounts > 0 for `type = 'Order'` transactions
- **Impact**: Customer gift card balances may be incorrect, revenue tracking issues

## Solution Components

### 1. Analysis Tools

#### **CSV Test Script** (`scripts/test_csv_parsing.php`)
- Validates CSV file structure and format
- Identifies potential discrepancies quickly
- **Usage**: `php scripts/test_csv_parsing.php`

#### **Analysis Script** (`scripts/analyze_gift_card_discrepancies.php`)
- Comprehensive discrepancy analysis
- Matches POS and database records using PaymentNumber format
- Generates detailed CSV reports

#### **Manager Script** (`scripts/gift_card_discrepancy_manager.php`)
- Command-line interface for all operations
- **Usage**: 
  ```bash
  php scripts/gift_card_discrepancy_manager.php analyze
  php scripts/gift_card_discrepancy_manager.php fix --dry-run
  php scripts/gift_card_discrepancy_manager.php fix --live
  ```

### 2. Fix Implementation

#### **Fix Script** (`scripts/fix_gift_card_discrepancies.php`)
- Re-sends correct transactions to POS system
- Uses existing `GiftCardController::voucherPayment()` pattern
- Creates audit trail with new transaction records

#### **Laravel Artisan Command** (`app/Console/Commands/FixGiftCardDiscrepancies.php`)
- Integrated Laravel command for production use
- **Usage**:
  ```bash
  php artisan giftcard:fix-discrepancies analyze
  php artisan giftcard:fix-discrepancies fix --dry-run
  php artisan giftcard:fix-discrepancies fix --live
  php artisan giftcard:fix-discrepancies both --dry-run
  ```

## Key Features

### Safety Mechanisms
- **Dry Run Default**: All fix operations simulate changes first
- **Confirmation Prompts**: Live mode requires explicit confirmation
- **Unique Payment Numbers**: Fixes use timestamped payment numbers to avoid conflicts
- **Audit Trail**: All operations create comprehensive CSV reports with current balances and database records
- **Balance Caching**: POS API calls are cached to avoid repeated requests and rate limiting
- **Rate Limiting Protection**: Automatic delays between API calls to prevent overloading POS system
- **Balance Validation**: Automatically detects and flags gift cards with insufficient funds for charged amounts

### Matching Logic
- **PaymentNumber Format**: `{last_4_digits_of_gift_card_code}_{last_4_digits_of_order_id}`
- **Example**: Gift Card `E1234567890123456` + Order `7890` = PaymentNumber `3456_7890`

### Error Handling
- Graceful handling of missing records
- POS API error capture and reporting
- Database connection and query error handling
- Detailed error logging in reports

## Current Status

### Test Results
- **CSV File**: Successfully parsed `eichlersapi.csv`
- **Total Transactions**: 185 rows processed
- **Potential Discrepancies**: 125 transactions identified
- **Format Validation**: All payment numbers follow expected format

### Files Created
1. `scripts/test_csv_parsing.php` - CSV validation tool
2. `scripts/analyze_gift_card_discrepancies.php` - Analysis engine
3. `scripts/fix_gift_card_discrepancies.php` - Fix implementation
4. `scripts/gift_card_discrepancy_manager.php` - CLI interface
5. `app/Console/Commands/FixGiftCardDiscrepancies.php` - Laravel command
6. `scripts/README.md` - Detailed documentation

## Next Steps

### 1. Run Analysis (Recommended First Step)
```bash
# Using Laravel command (recommended)
php artisan giftcard:fix-discrepancies analyze

# Or using standalone script
php scripts/gift_card_discrepancy_manager.php analyze
```

### 2. Review Results
- Check generated CSV report for discrepancy details
- Verify the identified transactions match expectations
- Confirm amounts and gift card codes are correct

### 3. Test Fixes (Dry Run)
```bash
# Test fixes without making actual changes
php artisan giftcard:fix-discrepancies fix --dry-run
```

### 4. Execute Fixes (When Ready)
```bash
# Make actual POS API calls to fix discrepancies
php artisan giftcard:fix-discrepancies fix --live
```

## Expected Outcomes

Based on the test results, this solution should:
- **Identify**: Up to 125 discrepant transactions
- **Fix**: Re-send correct charge amounts to POS system
- **Audit**: Create complete transaction history for all fixes
- **Report**: Generate detailed CSV reports for review

## Integration Notes

- Uses existing `GiftCardController::voucherPayment()` method pattern
- Creates `type = 'Fix'` transaction records in database
- Updates order metadata with fix details
- Maintains compatibility with current POS integration

## Support & Troubleshooting

### Common Issues
1. **CSV File Not Found**: Ensure `eichlersapi.csv` is in the correct location
2. **No Discrepancies Found**: Verify CSV format and database connection
3. **POS API Failures**: Check API credentials and network connectivity
4. **Database Errors**: Verify Laravel database configuration

### Debug Commands
```bash
# Verbose output for troubleshooting
php artisan giftcard:fix-discrepancies analyze --verbose

# Test CSV parsing first
php scripts/test_csv_parsing.php
```

## Files Generated

### Analysis Reports
- `gift_card_discrepancy_report_YYYY-MM-DD_HH-MM-SS.csv`

### Fix Reports
- `gift_card_fix_report_YYYY-MM-DD_HH-MM-SS.csv`

## Security Considerations

- All POS API calls use existing authentication
- Fix operations create unique payment numbers to avoid conflicts
- Database transactions maintain referential integrity
- All operations are logged for audit purposes

---

**Status**: ✅ Ready for testing and implementation
**Test Results**: 125 potential discrepancies identified
**Recommendation**: Start with analysis, then dry-run testing before live execution
