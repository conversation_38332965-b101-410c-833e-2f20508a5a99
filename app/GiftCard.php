<?php

namespace App;

use App\Http\Controllers\Api\GiftCardController;
use App\Jobs\SendEmail;
use Illuminate\Database\Eloquent\Model;

class GiftCard extends Model
{
    public $guarded = [];

    public function order()
    {
        return $this->belongsTo('App\Order');
    }

    public function Transactions()
    {
        return $this->hasMany(GiftCardTransaction::class);
    }

    public static function getCards($cards)
    {
        return collect($cards)->map(function ($card) {
            return self::getCard($card);
        });
    }

    public static function getCard($code)
    {
        $card = static::where('code', '=', $code)->first();
        if (!$card) {
            abort(400, 'Not valid gift card');
        }
        if ($card->balance == 0) {
            abort(400, 'Your balance is empty');
        }
        return $card;
    }

    public function chargeBalance($charge, $order, $transaction)
    {
        try {
            $original = $this->balance;

            $charge = +number_format($charge, 2, '.', '');

            // GiftCardController::voucherPayment($this->code, ($original - $this->balance), $order, $transaction);
            GiftCardController::voucherPayment($this->code, $charge, $order, $transaction);

            $remainder = 0;
            if ($this->balance >= $charge) {
                $this->balance = $this->balance - $charge;
            } else {
                $remainder = $charge - $this->balance;
                $this->balance = 0;
            }
            $this->save();
            return $remainder;
        } catch (\Exception $ex) {
            $transaction->update([
                'status' => 'cancelled',
                'reason' => $ex->getMessage()
            ]);
            throw $ex;
        }
    }

    public static function createUuid()
    {
        return 'E' . (string)rand(20000000, 99999999) . (string)rand(10000000, 99999999);
    }

    public static function createUniqueUuid()
    {
        while (true) {
            $code = static::createUuid();
            $obj = static::where('code', '=', $code)->first();
            if (!$obj) {
                break;
            }
        }
        return $code;
    }

    public static function getGiftCardAttribute($amount, $media = null, $quantity, $meta)
    {
        return [
            'type' => 'giftCard',
            'item_type' => 'digital',
            'id' => null,
            'media' => $media,
            'price' => $amount,
            'total' => $amount * $quantity,
            'quantity' => $quantity,
            'meta' => $meta,
            'title' => 'Eichler eGift Card',
            'vendor' => 'Eichlers',
            'path' => 'path',
        ];
    }

    public static function saveFromOrder($order)
    {
        $products = collect($order->products)->map(function ($product) use ($order) {
            if (data_get($product, 'type') == 'giftCard') {
                $product['giftCardIds'] = collect();
                collect(range(1, $product['quantity']))->each(function () use ($order, $product) {
                    $giftCard = $order->giftCards()->create([

                        'media' => data_get($product, 'media'),
                        'amount' => $product['amount'],
                        'balance' => $product['amount'],
                        'to_name' => $product['to_name'],
                        'from_name' => $product['from_name'],
                        'message' => $product['message'],
                        'to_email' => $product['to_email'],
                        'code' => static::createUniqueUuid(),
                    ]);

                    $product['giftCardIds']->push([
                        'id' => $giftCard->id,
                        'code' => substr($giftCard->code, -4),
                    ]);

                    $transaction = $giftCard->transactions()->create([
                        'order_id' => $order->id,
                        'type' => 'Create'
                    ]);


                    GiftCardController::createVoucher($transaction);
                    return $product;
                });
            }
            return $product;
        });
        $order->update([
            'products' => $products
        ]);
        // foreach ($order->products as $item) {
        //     if (data_get($item, 'type') == 'giftCard') {
        //         for ($i = 0; $i < $item['quantity']; $i++) {
        //             $giftCard = $order->giftCards()->create([

        //                 'media' => data_get($item, 'media'),
        //                 'amount' => $item['amount'],
        //                 'balance' => $item['amount'],
        //                 'to_name' => $item['to_name'],
        //                 'from_name' => $item['from_name'],
        //                 'message' => $item['message'],
        //                 'to_email' => $item['to_email'],
        //                 'code' => static::createUniqueUuid(),
        //             ]);

        //             $giftCard->transactions()->create([
        //                 'order_id' => $order->id,
        //                 'type' => 'Create'
        //             ]);


        //             GiftCardController::createVoucher($giftCard);
        //         }
        //     }
        // };
    }

    public static function getFrontEndAttribute($meta = null)
    {
        if ($meta) {
            return [
                'id' => data_get($meta, 'id'),
                'media' => data_get($meta, 'media'),
                'price' => data_get($meta, 'amount'),
                'amount' => data_get($meta, 'amount'),
                'to_name' => data_get($meta, 'to_name'),
                'message' => data_get($meta, 'message'),
                'to_email' => data_get($meta, 'to_email'),
                'quantity' => data_get($meta, 'quantity'),
                'from_name' => data_get($meta, 'from_name'),
                'total' => data_get($meta, 'quantity') * data_get($meta, 'amount'),
                'max' => 100,
                'type' => 'giftCard',
                'item_type' => 'digital',
                'vendor' => 'Eichlers',
                'path' => '/e-gift-card',
                'title' => 'Eichlers eGift Card',
            ];
        } else {
            return [
                'id' => self::$id,
                'media' => self::$media,
                'price' => self::$amount,
                'amount' => self::$amount,
                'to_name' => self::$to_name,
                'message' => self::$message,
                'to_email' => self::$to_email,
                'quantity' => self::$quantity,
                'from_name' => self::$from_name,
                'total' => self::$quantity * self::$amount,

                'type' => 'giftCard',
                'item_type' => 'digital',
                'vendor' => 'Eichlers',
                'path' => '/e-gift-card',
                'title' => 'Eichlers eGift Card',
            ];
        }
    }

    public function getExtendedDuration()
    {
        return 0;
    }

    public function void()
    {
        GiftCardController::voucherVoid($this);
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($giftCard) {
            if ($giftCard->getOriginal('to_email') != $giftCard->to_email && request()->is('nova-api/*')) {
                $data = [
                    'gift_card' => $giftCard,
                    'email' => $giftCard->to_email
                ];
                SendEmail::dispatch(
                    Emails\GiftCardNoticeEmail::class,
                    $data,
                    ['email' => data_get($data, 'email')]
                );
            }
        });
    }
}
