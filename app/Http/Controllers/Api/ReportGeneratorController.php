<?php

namespace App\Http\Controllers\Api;

use App\Export;
use App\Exports\ReportsExport;
use App\GiftCard;
use App\Http\Controllers\Controller;
use App\Order;
use App\ZipCode;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Rap2hpoutre\FastExcel\FastExcel;

class ReportGeneratorController extends Controller
{
    public static $dateGroups = ['date' => 'm/d/Y', 'month' => 'Y:M', 'year' => 'Y'];

    public function exportSalesByDate(Request $request)
    {
        $header = "Sales By Date - From: " . $request->start . " - To: " . $request->end . " - Grouped by " . ucfirst(
                $request->dateGroup
            );
        $headers = [
            [
                $header
            ],
            [
                'Date',
                'Orders',
                'Gross Sales',
                'Discount Amount',
                'Net Sales',
                'Shipping',
                'Tax',
                'Average Sales',
                'Total Sales'
            ]
        ];

        $sales = collect($this->salesByDate($request))->prepend($headers)->toArray();
        // return;
        (new ReportsExport($sales))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function salesByDate(Request $request)
    {
        $start = $request->start;

        $end = $request->end;

        $dateGroup = $request->dateGroup;

        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();

        $listing = $orders->groupBy(function ($order) use ($dateGroup) {
            if ($dateGroup != 'week' && $dateGroup != 'all') {
                return $order->created_at->format(self::$dateGroups[$dateGroup]);
            } elseif ($dateGroup == 'week') {
                return $this->weekFilter($order);
            } else {
                return 'All';
            }
        })->map(function ($group, $key) {
            return [
                'date' => $key,
                'orders' => $group->count(),
                'gross_net' => $group->sum('sub_total') ?? 0.00,
                'discount_amount' => $group->sum(function ($item) {
                        if (data_get($item, 'discount.type') != 'freeShipping') {
                            return $item->discount_amount;
                        }
                    }) ?? 0.00,
                'net_sales' => $group->sum('grand_total') - $group->sum('tax_amount') - $group->sum(
                        'shipping_amount'
                    ) ?? 0.00,
                'shipping' => $group->sum('shipping_amount') ?? 0.00,
                'tax' => $group->sum('tax_amount') ?? 0.00,
                'average_sales' => $group->avg('grand_total') ?? 0.00,
                'total_sales' => $group->sum('grand_total') ?? 0.00
            ];
        });
        $total = [
            [
                'date' => 'total',
                'orders' => $listing->sum('orders'),
                'gross_net' => $listing->sum('gross_net') ?? 0.00,
                'discount_amount' => $listing->sum('discount_amount') ?? 0.00,
                'net_sales' => $listing->sum('net_sales') ?? 0.00,
                'shipping' => $listing->sum('shipping') ?? 0.00,
                'tax' => $listing->sum('tax') ?? 0.00,
                'average_sales' => $listing->avg('total_sales') ?? 0.00,
                'total_sales' => $listing->sum('total_sales') ?? 0.00
            ]
        ];
        // dd(collect(array_merge($total, $listing->toArray())));
        $this->createReport(collect(array_merge($total, $listing->toArray())));
        return ['totals' => $total, 'list' => $listing];
    }

    public function weekFilter($order)
    {
        return $order->created_at->format('Y: ')
            . $order->created_at->startOfWeek()->format('M d - ')
            . $order->created_at->endOfWeek()->format('M d');
    }

    public function createReport($options)
    {
        dispatch(function () use ($options) {
            (new FastExcel($this->getUsersOneByOne($options)))->export(
                storage_path("app/exports.csv")
            );//->export('storage/app/exports.csv');
            (new ReportGeneratorController)->create('Inventory Export');
        });
    }

    public function getUsersOneByOne($options)
    {
        foreach ($options as $option) {
            $object = [];
            foreach ($option as $key => $value) {
                $object[$key] = $value;
            }
            yield $object;
        }
    }

    public function create($name)
    {
        $export = Export::create([
            'name' => $name,
        ]);

        try {
            $export->addMedia(storage_path("app/exports.csv"))->toMediaCollection('file');
            Storage::delete('exports.csv');
        } catch (Exception $ex) {
            $export->delete();
        }
    }

    public function exportSalesByProduct(Request $request)
    {
        $header = "Sales By Product - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [
                $header
            ],
            [
                'Title',
                'Type',
                'Vendor',
                'Net Quantity',
                'Gross Sales',
                'Discounts',
                'Net Sales',
                'Tax',
                'Total Sales'
            ]
        ];

        $sales = collect($this->salesByProduct($request))->prepend($headers)->toArray();

        (new ReportsExport($sales))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function salesByProduct(Request $request)
    {
        $start = $request->start;
        $end = $request->end;

        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();

        $orders = $orders->where('status', '!=', 'cancelled')->whereJsonLength('product_ids', '>', 0)
            ->pluck('products')->flatten(1)->where('product_id');
        $products = $orders->pluck('product_id')->unique();
        $listings = $products->map(function ($product) use ($orders) {
            $sales = $orders->where('product_id', '=', $product);
            return [
                'title' => $sales->first()['title'],
                'type' => $sales->first()['item_type'] ?? 'physical',
                'vendor' => $sales->first()['vendor'],
                'quantity' => $sales->sum('quantity'),
                'gross_sales' => $sales->sum('total') ?? 0.00,
                'discounts' => $sales->sum('discount_amount') ?? 0.00,
                'net_sales' => $sales->sum(function ($item) {
                        return $item['total'] - $item['discount_amount'];
                    }) ?? 0.00,
                'tax' => $sales->sum('tax_amount') ?? 0.00,
                'total_sales' => $sales->sum(function ($item) {
                        return ($item['total'] - $item['discount_amount']) + $item['tax_amount'];
                    }) ?? 0.00
            ];
        });
        $summary = [
            'title' => 'Summary',
            'type' => '',
            'vendor' => '',
            'net_quantity' => $listings->sum('quantity'),
            'gross_sales' => $listings->sum('gross_sales') ?? 0.00,
            'discounts' => $listings->sum('discounts') ?? 0.00,
            'net_sales' => $listings->sum('net_sales') ?? 0.00,
            'tax' => $listings->sum('tax') ?? 0.00,
            'total_sales' => $listings->sum('total_sales') ?? 0.00
        ];
        return ['summary' => $summary, 'list' => $listings];
    }

    public function exportSalesByVendor(Request $request)
    {
        $header = "Sales By Vendor - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [
                $header
            ],
            [
                'Vendor',
                'Net Quantity',
                'Gross Sales',
                'Discounts',
                'Net Sales',
                'Tax',
                'Total Sales'
            ]
        ];

        $sales = collect($this->salesByVendor($request))->prepend($headers)->toArray();

        (new ReportsExport($sales))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function salesByVendor(Request $request)
    {
        $start = $request->start;
        $end = $request->end;

        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();

        $orders = $orders->whereJsonLength('product_ids', '>', 0)
            ->pluck('products')->flatten(1)->where('product_id');
        $products = $orders->pluck('vendor')->filter()->unique();
        $listings = $products->map(function ($vendor) use ($orders) {
            $sales = $orders->where('vendor', '=', $vendor);
            return [
                'vendor' => $vendor,
                'quantity' => $sales->sum('quantity'),
                'gross_sales' => $sales->sum('total') ?? 0.00,
                'discounts' => $sales->sum('discount_amount') ?? 0.00,
                'net_sales' => $sales->sum(function ($item) {
                        return $item['total'] - $item['discount_amount'];
                    }) ?? 0.00,
                'tax' => $sales->sum('tax_amount') ?? 0.00,
                'total_sales' => $sales->sum(function ($item) {
                        return ($item['total'] - $item['discount_amount']) + $item['tax_amount'];
                    }) ?? 0.00
            ];
        });
        $summary = [
            'vendor' => 'Summary',
            'net_quantity' => $listings->sum('quantity'),
            'gross_sales' => $listings->sum('gross_sales') ?? 0.00,
            'discounts' => $listings->sum('discounts') ?? 0.00,
            'net_sales' => $listings->sum('net_sales') ?? 0.00,
            'tax' => $listings->sum('tax') ?? 0.00,
            'total_sales' => $listings->sum('total_sales') ?? 0.00
        ];
        return ['summary' => $summary, 'list' => $listings];
    }

    public function exportSalesByVariation(Request $request)
    {
        $header = "Sales By Variation - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [
                $header
            ],
            [
                'Title',
                'SKU',
                'Net Quantity',
                'Gross Sales',
                'Discounts',
                'Net Sales',
                'Tax',
                'Total Sales'
            ]
        ];

        $sales = collect($this->salesByVariation($request))->prepend($headers)->toArray();

        (new ReportsExport($sales))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function salesByVariation(Request $request)
    {
        $start = $request->start;
        $end = $request->end;

        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();

        $orders = $orders->whereRaw('JSON_CONTAINS(products->"$[*]",JSON_OBJECT("type","variation"))')
            ->pluck('products')->flatten(1)->where('type', '=', 'variation');
        $variations = $orders->pluck('id')->unique();
        $listings = $variations->map(function ($variation) use ($orders) {
            $sales = $orders->where('id', '=', $variation);
            return [
                'title' => $sales->first()['title'],
                'sku' => $sales->first()['sku'],
                'quantity' => $sales->sum('quantity'),
                'gross_sales' => $sales->sum('total') ?? 0.00,
                'discounts' => $sales->sum('discount_amount') ?? 0.00,
                'net_sales' => $sales->sum(function ($item) {
                        return $item['total'] - $item['discount_amount'];
                    }) ?? 0.00,
                'tax' => $sales->sum('tax_amount') ?? 0.00,
                'total_sales' => $sales->sum(function ($item) {
                        return ($item['total'] - $item['discount_amount']) + $item['tax_amount'];
                    }) ?? 0.00
            ];
        });

        $summary = [
            'title' => 'Summary',
            'sku' => '',
            'net_quantity' => $listings->sum('quantity') ?? 0.00,
            'gross_sales' => $listings->sum('gross_sales') ?? 0.00,
            'discounts' => $listings->sum('discounts') ?? 0.00,
            'net_sales' => $listings->sum('net_sales'),
            'tax' => $listings->sum('tax') ?? 0.00,
            'total_sales' => $listings->sum('total_sales') ?? 0.00
        ];
        return ['summary' => $summary, 'list' => $listings];
    }

    public function exportSalesByDiscount(Request $request)
    {
        $header = "Sales By Discount - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [
                $header
            ],
            [
                'Name',
                'Discount Applied',
                'Type',
                'Orders',
                'Gross Sales',
                'Discounts',
                'Net Sales',
                'Shipping',
                'Shipping Discounts',
                'Tax',
                'Total Sales'
            ]
        ];

        $sales = collect($this->salesByDiscount($request))->prepend($headers)->toArray();

        (new ReportsExport($sales))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function salesByDiscount(Request $request)
    {
        $start = $request->start;
        $end = $request->end;
        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();
        $orders = $orders->whereJsonLength('discount', '>', 0)->get();
        $discounts = $orders->pluck('discount_id')->unique();
        $listings = $discounts->map(function ($discount) use ($orders) {
            $sales = $orders->where('discount_id', $discount);
            $firstSale = $sales->first()->discount;
            return [
                'name' => $firstSale['name'],
                'discount_applied' => $firstSale['automated'] ? 'Automated' : 'Code',
                'type' => $firstSale['type'],
                'orders' => $sales->count(),
                'gross_sales' => $sales->sum('sub_total') ?? 0.00,
                'dsicount_amount' => $sales->sum('discount_amount') ?? 0.00,
                'net_sales' => $sales->sum('grand_total') - $sales->sum('tax_amount') - $sales->sum(
                        'shipping_amount'
                    ) ?? 0.00,
                'shipping' => $sales->sum(function ($sale) {
                        return $sale->shipping['shippingType']['price'];
                    }) ?? 0.00,
                'shipping_discounts' => $firstSale['type'] == 'freeShipping' ? $sales->sum('discount_amount') : 0.00,
                'tax' => $sales->sum('tax_amount') ?? 0.00,
                'total_sales' => $sales->sum('grand_total') ?? 0.00
            ];
        });
        $summary = [
            'name' => 'Summary',
            'discount_applied' => '',
            'type' => '',
            'orders' => $listings->sum('orders'),
            'gross_sales' => $listings->sum('gross_sales') ?? 0.00,
            'discount_amount' => $listings->sum('discount_amount') ?? 0.00,
            'net_sales' => $listings->sum('net_sales') ?? 0.00,
            'shipping' => $listings->sum('shipping') ?? 0.00,
            'shipping_discount' => $listings->sum('shipping)discount') ?? 0.00,
            'tax' => $listings->sum('tax') ?? 0.00,
            'total_sales' => $listings->sum('total_sales') ?? 0.00
        ];
        return ['summary' => $summary, 'listings' => $listings];
    }

    public function exportSalesByCustomer(Request $request)
    {
        $header = "Sales By Customer - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [
                $header
            ],
            [
                'Name',
                'Email',
                'Orders',
                'Gross Sales',
                'Net Sales',
                'Total Sales'
            ]
        ];

        $sales = collect($this->salesByCustomer($request))->prepend($headers)->toArray();

        (new ReportsExport($sales))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function salesByCustomer(Request $request)
    {
        $start = $request->start;
        $end = $request->end;
        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();

        $customers = $orders->pluck('customer_id')->unique();
        $listings = $customers->map(function ($customerId) use ($orders) {
            $sales = $orders->where('customer_id', $customerId);
            $customer = $sales->first()->customer;

            return [
                'name' => data_get($customer, 'name'),
                'email' => data_get($customer, 'email'),
                'orders' => $sales->count(),
                'gross_sales' => $sales->sum('sub_total') ?? 0.00,
                'net_sales' => $sales->sum('grand_total') - $sales->sum('tax_amount') - $sales->sum(
                        'shipping_amount'
                    ) ?? 0.00,
                'total_sales' => $sales->sum('grand_total') ?? 0.00
            ];
        });
        $summary = [
            'name' => 'Summary',
            'email' => '',
            'orders' => $listings->sum('orders'),
            'gross_sales' => $listings->sum('gross_sales') ?? 0.00,
            'net_sales' => $listings->sum('net_sales') ?? 0.00,
            'total_sales' => $listings->sum('total_sales') ?? 0.00
        ];
        return ['summary' => $summary, 'listings' => $listings];
    }

    public function exportShippingByOrder(Request $request)
    {
        $header = "Shipping By Order - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [
                $header
            ],
            [
                'Order Id',
                'Shipping Zone',
                'Price',
                'Cost',
                'Shipping Discount',
                'Total'
            ]
        ];

        $shipping = collect($this->shippingByOrder($request))->prepend($headers)->toArray();

        (new ReportsExport($shipping))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function shippingByOrder(Request $request)
    {
        $start = $request->start;
        $end = $request->end;
        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();
        $orders = $orders->whereJsonLength('shipping', ">", 0);
        $shippingZones = $this->getShippingZones(
            $orders->pluck('shipping->shippingInfo->postal_code as postal_code')->unique()->values()
        );
        $listings = $orders->get()->map(function ($order) use ($shippingZones) {
            $postalCode = $order->shipping['shippingInfo']['postal_code'];
            $shippingZone = data_get(
                $shippingZones->whereIn(
                    'zip_code',
                    [$postalCode, substr($postalCode, 0, 4), substr($postalCode, 0, 3)]
                )->sortByDesc('zip_code')->first(),
                'shippingZone.name'
            );
            return [
                'order_id' => $order->id,
                'shipping_zone' => $shippingZone,
                'price' => data_get($order, 'shipping.shippingType.price') ?? 0.00,
                'cost' => data_get($order, 'shipping.tracking.shipmentCost') ?? 0.00,
                'shipping_discount' => data_get(
                    $order,
                    'discount.type'
                ) == 'freeShipping' ? $order->discount['savings'] : 0.00,
                'total' => $order->shipping_amount - data_get($order, 'shipping.tracking.shipmentCost') ?? 0.00
            ];
        });
        $summary = [
            'order_id' => 'Summary',
            'shipping_zone' => '',
            'price' => $listings->sum('price') ?? 0.00,
            'cost' => $listings->sum('cost') ?? 0.00,
            'shipping_discount' => $listings->sum('shipping_discount') ?? 0.00,
            'total' => $listings->sum('total') ?? 0.00
        ];
        return ['summary' => $summary, 'listings' => $listings];
    }

    public function getShippingZones($postalCodes)
    {
        $codes = $postalCodes->map(function ($code) {
            return [$code, substr($code, 0, 4), substr($code, 0, 3)];
        })->flatten()->unique();
        return ZipCode::with('shippingZone')->whereIn('zip_code', $codes)->get();
    }

    public function exportSalesByLocation(Request $request)
    {
        $header = "Sales By Location - From: " . $request->start . " - To: " . $request->end . " - Grouped by " . ucfirst(
                $request->locationGroup
            );
        $headers = [
            [
                $header
            ],
            [
                'Location',
                'Orders',
                'Gross Sales',
                'Discount Amount',
                'Net Sales',
                'Shipping',
                'Tax',
                'Total Sales'
            ]
        ];

        $sales = collect($this->salesByLocation($request))->prepend($headers)->toArray();

        (new ReportsExport($sales))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function salesByLocation(Request $request)
    {
        $start = $request->start;
        $end = $request->end;
        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();
        $locationGroup = $request->locationGroup;
        $orders = $orders->whereJsonLength('shipping', '>', 0);
        if ($locationGroup != 'shipping_zone') {
            $groupedOrders = $orders->get()->groupBy(function ($order) use ($locationGroup) {
                if ($locationGroup == 'state') {
                    return data_get($order, "shipping.shippingInfo.{$locationGroup}") . ', ' . data_get(
                            $order,
                            'shipping.shippingInfo.country'
                        );
                }
                return data_get($order, "shipping.shippingInfo.{$locationGroup}");
            });
        } else {
            $shippingZones = $this->getShippingZones(
                $orders->pluck('shipping->shippingInfo->postal_code as postal_code')->unique()->values()
            );
            $groupedOrders = $orders->get()->groupBy(function ($order) use ($shippingZones) {
                $postalCode = data_get($order, 'shipping.shippingInfo.postal_code');
                return data_get(
                    $shippingZones->whereIn(
                        'zip_code',
                        [$postalCode, substr($postalCode, 0, 4), substr($postalCode, 0, 3)]
                    )->sortByDesc('zip_code')->first(),
                    'shippingZone.name'
                );
            });
        }
        $listings = $groupedOrders->map(function ($group, $key) {
            return [
                'location' => $key,
                'quantity' => $group->count(),
                'gross_net' => $group->sum('sub_total') ?? 0.00,
                'discount_amount' => $group->sum(function ($order) {
                        if (data_get($order, 'discount.type') != 'freeShipping') {
                            return $order->discount['savings'];
                        }
                    }) ?? 0.00,
                'net_sales' => $group->sum('grand_total') - $group->sum('tax_amount') - $group->sum(
                        'shipping_amount'
                    ) ?? 0.00,
                'shipping' => $group->sum('shipping_amount') ?? 0.00,
                'tax' => $group->sum('tax_amount') ?? 0.00,
                'total_sales' => $group->sum('grand_total') ?? 0.00
            ];
        });
        $total = [
            'location' => 'Summary',
            'quantity' => $listings->sum('quantity'),
            'gross_net' => $listings->sum('gross_net') ?? 0.00,
            'discount_amount' => $listings->sum('disocount_amount') ?? 0.00,
            'net_sales' => $listings->sum('net_sales') ?? 0.00,
            'shipping' => $listings->sum('shipping') ?? 0.00,
            'tax' => $listings->sum('tax') ?? 0.00,
            'total_sales' => $listings->sum('total_sales') ?? 0.00
        ];
        return ['total' => $total, 'listings' => $listings];
    }

    public function exportGiftCardsBalance(Request $request)
    {
        $header = "Gift Cards Balance - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [
                $header
            ],
            [
                'Order',
                'Amount',
                'Balance'
            ]
        ];
        $orders = collect($this->giftCardsBalance($request))->prepend($headers)->toArray();

        (new ReportsExport($orders))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function giftCardsBalance(Request $request)
    {
        $start = $request->start;
        $end = $request->end;
        $cards = GiftCard::whereBetween('created_at', [$start, $end])->get();
        $listings = $cards->map(function ($card) {
            return [
                'order' => $card->order_id,
                'amount' => $card->amount,
                'balance' => $card->balance ?? 0.00
            ];
        });
        $summary = [
            'order' => 'Summary',
            'amount' => $listings->sum('amount'),
            'balance' => $listings->sum('balance') ?? 0.00
        ];
        return ['summary' => $summary, 'listings' => $listings];
    }

    public function exportGiftCardsTransactions(Request $request)
    {
        $header = "Gift Cards Transactions - From: " . $request->start . " - To: " . $request->end . " - Grouped By " . ucfirst(
                $request->dateGroup
            );
        $headers = [
            [
                $header
            ],
            [
                'Date',
                'Purchases',
                'Total Purchase',
                'Payments',
                'Total Payments'
            ]
        ];

        $transactions = collect($this->giftCardsTransactions($request))->prepend($headers)->toArray();

        (new ReportsExport($transactions))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Export started!');
    }

    public function giftCardsTransactions(Request $request)
    {
        $start = $request->start;
        $end = $request->end;
        $dateGroup = $request->dateGroup;
        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();
        $listings = $orders->get()
            ->groupBy(function ($key) use ($dateGroup) {
                if ($dateGroup != 'week' && $dateGroup != 'all') {
                    return $key->created_at->format(self::$dateGroups[$dateGroup]);
                } elseif ($dateGroup == 'week') {
                    return $this->weekFilter($key);
                } else {
                    return 'All';
                }
            })->map(function ($group, $key) {
                $purchases = $group->pluck('products')->flatten(1)->where('type', '=', 'giftCard');
                $payments = $group->pluck('payments')->pluck('giftCard')->filter()->flatten(1);
                return [
                    'date' => $key,
                    'purchases' => $purchases->count() ?? 0,
                    'total_purchase' => $purchases->sum('amount') ?? 0.00,
                    'payments' => $payments->count() ?? 0,
                    'total_payments' => $payments->sum('amount') ?? 0.00
                ];
            });
        $total = [
            'date' => 'Summary',
            'purchases' => $listings->sum('purchases') ?? 0,
            'total_purchase' => $listings->sum('total_purchase') ?? 0.00,
            'payments' => $listings->sum('payments') ?? 0,
            'total_payments' => $listings->sum('total_payments') ?? 0.00
        ];
        return ['total' => $total, 'listings' => $listings];
    }

    public function exportShippingAccuracy(Request $request)
    {
        $header = "Shipping Accuracy - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [
                $header
            ],
            [
                'Order Id',
                'Country - Postal Code',
                'Estimated Arrival',
                'Actual Arrival',
                'Accurate'
            ]
        ];

        $deliveries = collect($this->shippingAccuracy($request))->prepend($headers)->toArray();

        (new ReportsExport($deliveries))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);

        return back()->withSuccess('Export started!');

        // $export = new ReportsExport($deliveries);
        // return Excel::download($export, 'shippingAccuracy.csv');
    }

    public function shippingAccuracy(Request $request)
    {
        $start = $request->start;
        $end = $request->end;
        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();
        $listing = $orders->where('shipping->tracking->carrier', '<>', null)->get()
            ->map(function ($order) {
                $expected = new Carbon(data_get($order, 'shipping.shippingType.estimated_arrival'));
                $actual = new Carbon(data_get($order, 'shipping.tracking.arrived_at'));
                return [
                    'order' => $order->id,
                    'location' => $order->shipping['shippingInfo']['country'] . ' - ' . $order->shipping['shippingInfo']['postal_code'],
                    'estimated_arrival' => $expected->format('Y-m-d'),
                    'actual_arrival' => $actual->format('Y-m-d'),
                    'accurate' => $actual->isSameDay($expected) || $actual->isBefore($expected) ? '1' : '0'
                ];
            });
        $summary = [
            'order' => 'Summary',
            'location' => '',
            'estimated_arrival' => '',
            'actual_arrival' => '',
            'accurate' => $listing->count() ? number_format(
                    ($listing->sum('accurate') / $listing->count() * 100),
                    2
                ) . '%' : 0
        ];
        return [$summary, $listing];
    }

    public function apis()
    {
        Route::get('reports/sales_by_date', 'Api\\ReportGeneratorController@salesByDate');
        Route::get('reports/sales_by_date/export', 'Api\\ReportGeneratorController@exportSalesByDate');

        Route::get('reports/sales_by_product', 'Api\\ReportGeneratorController@salesByProduct');
        Route::get('reports/sales_by_product/export', 'Api\\ReportGeneratorController@exportSalesByProduct');

        Route::get('reports/sales_by_vendor', 'Api\\ReportGeneratorController@salesByVendor');
        Route::get('reports/sales_by_vendor/export', 'Api\\ReportGeneratorController@exportSalesByVendor');

        Route::get('reports/sales_by_variation', 'Api\\ReportGeneratorController@salesByVariation');
        Route::get('reports/sales_by_variation/export', 'Api\\ReportGeneratorController@exportSalesByVariation');

        Route::get('reports/sales_by_discount', 'Api\\ReportGeneratorController@salesByDiscount');
        Route::get('reports/sales_by_discount/export', 'Api\\ReportGeneratorController@exportSalesByDiscount');

        Route::get('reports/sales_by_customer', 'Api\\ReportGeneratorController@salesByCustomer');
        Route::get('reports/sales_by_customer/export', 'Api\\ReportGeneratorController@exportSalesByCustomer');

        Route::get('reports/sales_by_location', 'Api\\ReportGeneratorController@salesByLocation');
        Route::get('reports/sales_by_location/export', 'Api\\ReportGeneratorController@exportSalesByLocation');

        Route::get('reports/shipping_by_order', 'Api\\ReportGeneratorController@shippingByOrder');
        Route::get('reports/shipping_by_order/export', 'Api\\ReportGeneratorController@exportShippingByOrder');

        Route::get('reports/gift_cards_balance', 'Api\\ReportGeneratorController@giftCardsBalance');
        Route::get('reports/gift_cards_balance/export', 'Api\\ReportGeneratorController@exportGiftCardsBalance');

        Route::get('reports/gift_cards_transactions', 'Api\\ReportGeneratorController@giftCardsTransactions');
        Route::get(
            'reports/gift_cards_transactions/export',
            'Api\\ReportGeneratorController@exportGiftCardsTransactions'
        );

        Route::get('reports/shipping_accuracy', 'Api\\ReportGeneratorController@shippingAccuracy');
        Route::get('reports/shipping_accuracy/export', 'Api\\ReportGeneratorController@exportShippingAccuracy');
    }

    public function exportGiftCardUsageSummary(Request $request)
    {
        $request->merge([
            'start' => $request->start ?? '2025-07-01',
            'end' => $request->end ?? Carbon::now()->format('Y-m-d')
        ]);
        $header = "Gift Card Usage Summary - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [$header],
            [
                'Customer Name',
                'Customer Email', 
                'Order ID',
                'Gift Card Code',
                'Amount Charged',
                'Original Balance',
                'Remaining Balance',
                'Transaction Date'
            ]
        ];

        $data = collect($this->giftCardUsageSummary($request))->prepend($headers)->toArray();
        dd(
            collect($data)->map(function ($item) {
                return collect($item)->join(',');
            })->join('\n')
        );

        (new ReportsExport($data))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Gift Card Usage Summary export started!');
    }

    public function giftCardUsageSummary(Request $request)
    {
        $start = $request->start;
        $end = $request->end;
        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();
        
        $giftCardUsage = $orders->where('status', '!=', 'cancelled')
            ->filter(function ($order) {
                return collect($order->payments)->has('giftCard');
            })
            ->flatMap(function ($order) {
                $giftCardPayments = collect($order->payments['giftCard'] ?? []);
                
                return $giftCardPayments->map(function ($payment) use ($order) {
                    $giftCard = GiftCard::find($payment['id']);
                    
                    return [
                        'customer_name' => $order->customer->name ?? 'N/A',
                        'customer_email' => $order->customer->email ?? 'N/A',
                        'order_id' => $order->id,
                        'gift_card_code' => $giftCard->code,
                        'amount_charged' => $payment['amount'] ?? 0,
                        'original_balance' => $giftCard->amount ?? 0,
                        'remaining_balance' => $giftCard->balance ?? 0,
                        'transaction_date' => $order->created_at->format('Y-m-d H:i:s')
                    ];
                });
            });

        return $giftCardUsage->toArray();
    }

    public function exportGiftCardOveruseDetection(Request $request)
    {
        $request->merge([
            'start' => $request->start ?? '2025-07-01',
            'end' => $request->end ?? Carbon::now()->format('Y-m-d')
        ]);
        $header = "Gift Card Overuse Detection - From: " . $request->start . " - To: " . $request->end;
        $headers = [
            [$header],
            [
                'Gift Card Code',
                'Gift Card ID',
                'Original Amount',
                'Total Amount Charged',
                'Number of Uses',
                'Overuse Amount',
                'Order IDs',
                'Customer Names',
                'Customer Emails'
            ]
        ];

        $data = collect($this->giftCardOveruseDetection($request))->prepend($headers)->toArray();
        dd(
            collect($data)->map(function ($item) {
                return collect($item)->join(',');
            })->join('\n')
        );

        (new ReportsExport($data))->queue("exports.csv")->chain([
            dispatch(function () use ($header) {
                (new ReportGeneratorController)->create($header);
            }),
        ]);
        return back()->withSuccess('Gift Card Overuse Detection export started!');
    }

    public function giftCardOveruseDetection(Request $request)
    {
        $start = $request->start;
        $end = $request->end;
        
        $orders = $start ? Order::whereBetween('created_at', [$start, $end])->get() : Order::all();
        
        $giftCardUsage = $orders->where('status', '!=', 'cancelled')
            ->filter(function ($order) {
                return collect($order->payments)->has('giftCard');
            })
            ->flatMap(function ($order) {
                $giftCardPayments = collect($order->payments['giftCard'] ?? []);
                
                return $giftCardPayments->map(function ($payment) use ($order) {
                    $giftCard = GiftCard::find($payment['id']);
                    return [
                        'code' => $giftCard->code,
                        'id' => $giftCard->id,
                        'amount' => $payment['amount'] ?? 0,
                        'order_id' => $order->id,
                        'customer_name' => $order->customer->name ?? 'N/A',
                        'customer_email' => $order->customer->email ?? 'N/A'
                    ];
                });
            })
            ->groupBy('code')
            ->map(function ($usages, $code){
                $giftCard = GiftCard::firstwhere('code', $code);
                $originalAmount = $giftCard->amount ?? 0;
                $totalCharged = $usages->sum('amount');
                $overuseAmount = $totalCharged - $originalAmount;
                
                return [
                    'gift_card_code' => $code,
                    'gift_card_id' => $giftCard->id,
                    'original_amount' => $originalAmount,
                    'total_amount_charged' => $totalCharged,
                    'number_of_uses' => $usages->count(),
                    'overuse_amount' => $overuseAmount,
                    'order_ids' => '"'. $usages->pluck('order_id')->implode(', ') . '"',
                    'customer_names' => $usages->pluck('customer_name')->unique()->implode(', '),
                    'customer_emails' => $usages->pluck('customer_email')->unique()->implode(', ')
                ];
            })
            ->filter(function ($item) {
                return $item['overuse_amount'] > 0;
            });

        return $giftCardUsage->values()->toArray();
    }
}
