<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\GiftCard;
use App\GiftCardTransaction;
use App\Order;
use App\Http\Controllers\Api\GiftCardController;
use Illuminate\Http\Request;
use Exception;

class FixGiftCardDiscrepancies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'giftcard:fix-discrepancies
                            {action : Action to perform (analyze|fix|both)}
                            {--csv-file=eichlersapi.csv : Path to POS CSV export file}
                            {--live : Execute actual POS API calls (default is dry-run)}
                            {--force : Skip confirmation prompts}
                            {--clear-cache : Clear the gift card balance cache before starting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze and fix gift card transaction discrepancies between POS and database';

    private $discrepancies = [];
    private $posTransactions = [];
    private $dateFilterFrom = '2025-07-01 00:00:00';
    private $dbTransactions = [];
    private $balanceCache = [];
    private $cumulativeCharges = [];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Clear cache if requested
        if ($this->option('clear-cache')) {
            $this->clearBalanceCache();
            $this->info("🗑️  Gift card balance cache cleared.");
        }

        // Load existing cache
        $this->loadBalanceCache();

        $action = $this->argument('action');

        switch ($action) {
            case 'analyze':
                return $this->runAnalysis();
            case 'fix':
                return $this->runFix();
            case 'both':
                return $this->runBoth();
            default:
                $this->error("Invalid action. Use: analyze, fix, or both");
                return 1;
        }
    }

    private function runAnalysis()
    {
        $this->info('🔍 Analyzing gift card transaction discrepancies...');
        $this->newLine();

        try {
            $this->parsePosData();
            $this->info("✅ Found " . count($this->posTransactions) . " successful POS transactions with ChargeAmount = 0");

            $this->queryDatabaseTransactions();
            $this->info("✅ Found " . count($this->dbTransactions) . " database transactions with type='Order' and amount > 0");

            $this->matchRecords();
            
            if (empty($this->discrepancies)) {
                $this->info("🎉 No discrepancies found! All transactions are in sync.");
                return 0;
            }

            $this->warn("⚠️  Found " . count($this->discrepancies) . " discrepancies");
            $this->generateReport();

            // Run cumulative charge analysis
            $this->analyzeCumulativeCharges();
            $this->generateCumulativeChargeReport();

            return 0;

        } catch (Exception $e) {
            $this->error("❌ Error during analysis: " . $e->getMessage());
            return 1;
        }
    }

    private function runFix()
    {
        $isLive = $this->option('live');
        
        $this->info('🔧 ' . ($isLive ? 'Fixing' : 'Simulating fixes for') . ' gift card discrepancies...');
        
        if ($isLive && !$this->option('force')) {
            if (!$this->confirm('⚠️  This will make actual POS API calls. Continue?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        try {
            // First analyze to get discrepancies
            $this->runAnalysis();
            
            if (empty($this->discrepancies)) {
                return 0;
            }

            $this->newLine();
            $this->info("🔧 Processing " . count($this->discrepancies) . " discrepancies...");

            $results = [];
            $progressBar = $this->output->createProgressBar(count($this->discrepancies));
            $progressBar->start();

            foreach ($this->discrepancies as $index => $discrepancy) {
                $result = $this->processDiscrepancy($discrepancy, $index + 1, $isLive);
                $results[] = $result;
                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine(2);

            $this->generateFixReport($results, $isLive);

            // Run cumulative charge analysis after fixes
            $this->analyzeCumulativeCharges();
            $this->generateCumulativeChargeReport();

            return 0;

        } catch (Exception $e) {
            $this->error("❌ Error during fix: " . $e->getMessage());
            return 1;
        }
    }

    private function runBoth()
    {
        $this->info('🔍🔧 Running complete analysis and fix process...');
        $this->newLine();

        $analysisResult = $this->runAnalysis();
        if ($analysisResult !== 0) {
            return $analysisResult;
        }

        if (!empty($this->discrepancies)) {
            $this->newLine();
            return $this->runFix();
        }

        return 0;
    }

    private function parsePosData()
    {
        $csvFile = $this->option('csv-file');

        if (!file_exists($csvFile)) {
            throw new Exception("CSV file not found: {$csvFile}");
        }

        $handle = fopen($csvFile, 'r');
        $header = fgetcsv($handle);
        $filteredOutCount = 0;

        while (($row = fgetcsv($handle)) !== false) {
            $data = array_combine($header, $row);
            $jsonData = json_decode($data['data'], true);

            if ($data['ResponseCode'] == '200' &&
                isset($jsonData['ChargeAmount']) &&
                $jsonData['ChargeAmount'] == 0) {

                // Apply date filter
                $transactionDate = date('Y-m-d H:i:s', strtotime($data['CreatedOn']));
                if ($transactionDate < $this->dateFilterFrom) {
                    $filteredOutCount++;
                    continue; // Skip transactions before July 1, 2025
                }

                $this->posTransactions[] = [
                    'voucher_number' => $jsonData['VoucherNumber'],
                    'payment_number' => $jsonData['PaymentNumber'],
                    'charge_amount' => $jsonData['ChargeAmount'],
                    'response_code' => $data['ResponseCode'],
                    'created_on' => $data['CreatedOn']
                ];
            }
        }
        fclose($handle);

        if ($filteredOutCount > 0) {
            $this->info("📅 Filtered out {$filteredOutCount} POS transactions before July 1, 2025");
        }
    }

    private function queryDatabaseTransactions()
    {
        $this->dbTransactions = DB::table('gift_card_transactions as gct')
            ->join('gift_cards as gc', 'gct.gift_card_id', '=', 'gc.id')
            ->join('orders as o', 'gct.order_id', '=', 'o.id')
            ->where('gct.type', 'Order')
            ->where('gct.amount', '>', 0)
            ->where('gct.created_at', '>=', $this->dateFilterFrom)
            ->select([
                'gct.id as transaction_id',
                'gct.order_id',
                'gct.gift_card_id',
                'gct.amount',
                'gct.created_at',
                'gc.code as gift_card_code'
            ])
            ->get()
            ->toArray();
    }

    private function matchRecords()
    {
        foreach ($this->posTransactions as $posTransaction) {
            $paymentNumber = $posTransaction['payment_number'];
            
            if (strpos($paymentNumber, 'VOID') !== false) {
                continue;
            }

            $parts = explode('_', $paymentNumber);
            if (count($parts) !== 2) {
                continue;
            }

            $giftCardLast4 = $parts[0];
            $orderLast4 = $parts[1];

            foreach ($this->dbTransactions as $dbTransaction) {
                $dbGiftCardLast4 = substr($dbTransaction->gift_card_code, -4);
                $dbOrderLast4 = substr($dbTransaction->order_id, -4);

                if ($dbGiftCardLast4 === $giftCardLast4 && $dbOrderLast4 === $orderLast4) {
                    $this->discrepancies[] = [
                        'gift_card_code' => $dbTransaction->gift_card_code,
                        'order_id' => $dbTransaction->order_id,
                        'expected_amount' => $dbTransaction->amount,
                        'pos_charge_amount' => $posTransaction['charge_amount'],
                        'transaction_id' => $dbTransaction->transaction_id,
                        'gift_card_id' => $dbTransaction->gift_card_id,
                        'pos_payment_number' => $posTransaction['payment_number']
                    ];
                    break;
                }
            }
        }
    }

    private function processDiscrepancy($discrepancy, $number, $isLive)
    {
        try {
            $giftCard = GiftCard::where('code', $discrepancy['gift_card_code'])->first();
            $transaction = GiftCardTransaction::find($discrepancy['transaction_id']);
            $order = Order::find($discrepancy['order_id']);

            if (!$giftCard || !$transaction || !$order) {
                throw new Exception("Required records not found");
            }

            if ($isLive) {
                $result = $this->sendPosPayment($giftCard, $transaction, $order, $discrepancy['expected_amount']);
            } else {
                $result = [
                    'status' => 'dry_run',
                    'message' => 'Dry run - no actual POS call made'
                ];
            }

            return [
                'discrepancy_number' => $number,
                'gift_card_code' => $discrepancy['gift_card_code'],
                'order_id' => $discrepancy['order_id'],
                'amount' => $discrepancy['expected_amount'],
                'result' => $result,
                'success' => $result['status'] === 'success' || $result['status'] === 'dry_run'
            ];

        } catch (Exception $e) {
            return [
                'discrepancy_number' => $number,
                'gift_card_code' => $discrepancy['gift_card_code'] ?? 'unknown',
                'order_id' => $discrepancy['order_id'] ?? 'unknown',
                'amount' => $discrepancy['expected_amount'] ?? 0,
                'result' => [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ],
                'success' => false
            ];
        }
    }

    private function sendPosPayment($giftCard, $transaction, $order, $amount)
    {
        try {
            $paymentNumber = substr($giftCard->code, -4) . '_FIX_' . substr($order->id, -4) . '_' . time();

            $client = new \GuzzleHttp\Client;
            $json = [
                'VoucherNumber' => $giftCard->code,
                'ChargeAmount' => $amount,
                'SiteId' => 1,
                'PaymentNumber' => $paymentNumber,
                'TenderName' => 'Voucher',
            ];

            $response = $client->post("https://api5.firstchoicepos.com/v1/Voucher/UseForPayment", [
                'headers' => ['Authorization' => 'Basic ' . env('POS')],
                'json' => $json
            ]);

            $responseBody = $response->getBody()->getContents();

            // Update order and create fix transaction
            $order->update([
                "meta->pos_gift_card_fix->{$giftCard->code}" => [
                    'response' => $responseBody,
                    'json' => $json,
                    'fixed_at' => now(),
                    'original_transaction_id' => $transaction->id
                ]
            ]);

            GiftCardTransaction::create([
                'order_id' => $order->id,
                'gift_card_id' => $giftCard->id,
                'amount' => $amount,
                'type' => 'Fix',
                'pos_message' => [
                    'message' => $responseBody,
                    'json' => $json,
                    'original_transaction_id' => $transaction->id
                ]
            ]);

            return [
                'status' => 'success',
                'message' => 'POS payment sent successfully',
                'payment_number' => $paymentNumber
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    private function generateReport()
    {
        $this->newLine();
        $this->info("📊 DISCREPANCY SUMMARY:");
        
        $totalAmount = array_sum(array_column($this->discrepancies, 'expected_amount'));
        
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Discrepancies', count($this->discrepancies)],
                ['Total Amount Not Charged', '$' . number_format($totalAmount, 2)]
            ]
        );

        if (count($this->discrepancies) <= 10) {
            $this->newLine();
            $this->info("📋 DISCREPANCY DETAILS:");
            
            $tableData = [];
            foreach ($this->discrepancies as $index => $discrepancy) {
                $tableData[] = [
                    $index + 1,
                    $discrepancy['gift_card_code'],
                    $discrepancy['order_id'],
                    '$' . number_format($discrepancy['expected_amount'], 2)
                ];
            }
            
            $this->table(['#', 'Gift Card', 'Order ID', 'Amount'], $tableData);
        }
    }

    private function generateFixReport($results, $isLive)
    {
        $successful = array_filter($results, function($r) { return $r['success']; });
        $failed = array_filter($results, function($r) { return !$r['success']; });

        $this->info("📊 FIX RESULTS:");

        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Processed', count($results)],
                ['Successful', count($successful)],
                ['Failed', count($failed)],
                ['Total Amount ' . ($isLive ? 'Charged' : 'Would Charge'), '$' . number_format(array_sum(array_column($successful, 'amount')), 2)]
            ]
        );

        // Display detailed table of successfully charged gift cards
        if (!empty($successful)) {
            $this->newLine();
            $this->info("✅ SUCCESSFULLY " . ($isLive ? "CHARGED" : "WOULD CHARGE") . " GIFT CARDS:");

            // Show progress bar for balance retrieval
            $this->info("🔍 Retrieving current balances from POS system...");
            $progressBar = $this->output->createProgressBar(count($successful));
            $progressBar->start();

            $successfulTableData = [];
            foreach ($successful as $success) {
                // Validate success data structure
                $giftCardCode = $success['gift_card_code'] ?? 'Unknown';
                $orderId = $success['order_id'] ?? 'Unknown';
                $amount = $success['amount'] ?? 0;
                $resultStatus = $success['result']['status'] ?? 'unknown';

                // Skip if essential data is missing
                if ($giftCardCode === 'Unknown' || $orderId === 'Unknown' || $amount <= 0) {
                    $progressBar->advance();
                    continue;
                }

                // Get current balance from POS API
                $currentBalance = $this->getGiftCardBalance($giftCardCode);

                // Format balance for display
                $balanceDisplay = 'N/A';

                if ($currentBalance !== 'Error' && is_numeric($currentBalance)) {
                    $balanceDisplay = '$' . number_format($currentBalance, 2);
                } elseif ($currentBalance === 'Error') {
                    $balanceDisplay = 'Error';
                }

                // Get enhanced balance status (considers cumulative charges)
                $balanceStatus = $this->getEnhancedBalanceStatus(
                    $giftCardCode,
                    $amount,
                    $currentBalance
                );

                $successfulTableData[] = [
                    $giftCardCode,
                    $orderId,
                    '$' . number_format($amount, 2),
                    $balanceDisplay,
                    $balanceStatus,
                    $resultStatus === 'dry_run' ? 'Dry Run' : 'Success'
                ];

                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine(2);

            $this->table(
                ['Gift Card Code', 'Order ID', 'Amount Charged', 'Current Balance', 'Balance Status', 'Status'],
                $successfulTableData
            );

            // Show balance status summary
            $this->displayBalanceStatusSummary($successfulTableData);
        } else {
            $this->newLine();
            $this->info("ℹ️  No gift cards were successfully charged.");
        }

        if (!empty($failed)) {
            $this->newLine();
            $this->warn("❌ FAILED FIXES:");
            foreach ($failed as $failure) {
                $this->line("  #{$failure['discrepancy_number']}: {$failure['gift_card_code']} - {$failure['result']['message']}");
            }
        }
    }

    /**
     * Get gift card balance from POS API with caching
     */
    private function getGiftCardBalance($giftCardCode)
    {
        // Check if balance is already cached
        if (isset($this->balanceCache[$giftCardCode])) {
            return $this->balanceCache[$giftCardCode];
        }

        // Load cache from temporary file if it exists
        $this->loadBalanceCache();

        // Check cache again after loading from file
        if (isset($this->balanceCache[$giftCardCode])) {
            return $this->balanceCache[$giftCardCode];
        }

        // Make API call to get balance
        try {
            $controller = new GiftCardController();
            $request = new Request(['code' => $giftCardCode]);

            $balance = $controller->checkGiftCard($request);

            // Cache the result
            $this->balanceCache[$giftCardCode] = $balance;
            $this->saveBalanceCache();

            // Add small delay to avoid rate limiting
            usleep(250000); // 250ms delay

            return $balance;

        } catch (Exception $e) {
            // Cache the error result to avoid repeated failed calls
            $this->balanceCache[$giftCardCode] = 'Error';
            $this->saveBalanceCache();

            return 'Error';
        }
    }

    /**
     * Load balance cache from temporary file
     */
    private function loadBalanceCache()
    {
        $cacheFile = storage_path('app/gift_card_balance_cache.json');

        if (file_exists($cacheFile)) {
            $cacheData = json_decode(file_get_contents($cacheFile), true);
            if ($cacheData && is_array($cacheData)) {
                $this->balanceCache = $cacheData;
            }
        }
    }

    /**
     * Save balance cache to temporary file
     */
    private function saveBalanceCache()
    {
        $cacheFile = storage_path('app/gift_card_balance_cache.json');
        file_put_contents($cacheFile, json_encode($this->balanceCache, JSON_PRETTY_PRINT));
    }

    /**
     * Clear balance cache file
     */
    private function clearBalanceCache()
    {
        $cacheFile = storage_path('app/gift_card_balance_cache.json');
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
        $this->balanceCache = [];
    }

    /**
     * Export cumulative charge analysis results to CSV
     */
    private function exportCumulativeChargeResults()
    {
        if (empty($this->cumulativeCharges)) {
            $this->info("ℹ️  No cumulative charge data to export.");
            return;
        }

        $timestamp = date('Y-m-d_H-i-s');
        $filename = "cumulative_gift_card_analysis_{$timestamp}.csv";
        $filepath = storage_path("app/{$filename}");

        $this->info("📄 Exporting cumulative charge analysis to CSV...");

        // Separate cards by status
        $multiChargeCards = array_filter($this->cumulativeCharges, function($data) {
            return count($data['transactions']) > 1;
        });

        $insufficientCards = array_filter($multiChargeCards, function($data) {
            return $data['balance_status'] === 'insufficient';
        });

        $sufficientCards = array_filter($multiChargeCards, function($data) {
            return $data['balance_status'] === 'sufficient';
        });

        $unknownCards = array_filter($multiChargeCards, function($data) {
            return $data['balance_status'] === 'unknown';
        });

        // Create CSV file
        $handle = fopen($filepath, 'w');
        if (!$handle) {
            $this->error("❌ Could not create CSV file: {$filepath}");
            return;
        }

        // Add BOM for proper UTF-8 encoding in Excel
        fwrite($handle, "\xEF\xBB\xBF");

        // Write summary section
        fputcsv($handle, ['CUMULATIVE GIFT CARD CHARGE ANALYSIS SUMMARY']);
        fputcsv($handle, ['Generated', date('Y-m-d H:i:s')]);
        fputcsv($handle, ['Date Filter Applied', 'Transactions on or after July 1, 2025']);
        fputcsv($handle, ['Total Gift Cards Analyzed', count($this->cumulativeCharges)]);
        fputcsv($handle, ['Cards with Multiple Charges', count($multiChargeCards)]);
        fputcsv($handle, ['Cards with Insufficient Balance', count($insufficientCards)]);
        fputcsv($handle, ['Cards with Sufficient Balance', count($sufficientCards)]);
        fputcsv($handle, ['Cards with Unknown Balance', count($unknownCards)]);

        if (!empty($insufficientCards)) {
            $totalDeficit = array_sum(array_column($insufficientCards, 'deficit'));
            fputcsv($handle, ['Total Deficit Amount', '$' . number_format($totalDeficit, 2)]);
        }

        fputcsv($handle, []); // Empty row separator

        // Write headers for detailed data
        fputcsv($handle, [
            'Gift Card Code',
            'Transaction Count',
            'Total Cumulative Charges',
            'Current POS Balance',
            'Balance Status',
            'Deficit Amount',
            'Transaction Details'
        ]);

        // Write insufficient balance cards first (priority)
        foreach ($insufficientCards as $data) {
            $this->writeCumulativeChargeRow($handle, $data);
        }

        // Write sufficient balance cards
        foreach ($sufficientCards as $data) {
            $this->writeCumulativeChargeRow($handle, $data);
        }

        // Write unknown balance cards
        foreach ($unknownCards as $data) {
            $this->writeCumulativeChargeRow($handle, $data);
        }

        // Write single transaction cards (for completeness)
        $singleTransactionCards = array_filter($this->cumulativeCharges, function($data) {
            return count($data['transactions']) === 1;
        });

        if (!empty($singleTransactionCards)) {
            fputcsv($handle, []); // Empty row separator
            fputcsv($handle, ['SINGLE TRANSACTION CARDS (for reference)']);
            fputcsv($handle, [
                'Gift Card Code',
                'Transaction Count',
                'Total Cumulative Charges',
                'Current POS Balance',
                'Balance Status',
                'Deficit Amount',
                'Transaction Details'
            ]);

            foreach ($singleTransactionCards as $data) {
                $this->writeCumulativeChargeRow($handle, $data);
            }
        }

        fclose($handle);

        $this->newLine();
        $this->info("✅ Cumulative charge analysis exported successfully!");
        $this->info("📁 File location: {$filepath}");
        $this->info("📊 Export summary:");
        $this->info("   • Total records: " . count($this->cumulativeCharges));
        $this->info("   • Multi-charge cards: " . count($multiChargeCards));
        $this->info("   • Insufficient balance cards: " . count($insufficientCards));

        if (!empty($insufficientCards)) {
            $totalDeficit = array_sum(array_column($insufficientCards, 'deficit'));
            $this->warn("   • Total deficit: $" . number_format($totalDeficit, 2));
        }
    }

    /**
     * Write a cumulative charge data row to CSV
     */
    private function writeCumulativeChargeRow($handle, $data)
    {
        // Format current balance
        $currentBalanceDisplay = 'N/A';
        if ($data['current_balance'] !== null && $data['current_balance'] !== 'Error' && is_numeric($data['current_balance'])) {
            $currentBalanceDisplay = '$' . number_format($data['current_balance'], 2);
        } elseif ($data['current_balance'] === 'Error') {
            $currentBalanceDisplay = 'Error';
        }

        // Format balance status
        $balanceStatus = ucfirst($data['balance_status']);

        // Format deficit amount
        $deficitAmount = '';
        if (isset($data['deficit']) && $data['deficit'] > 0) {
            $deficitAmount = '$' . number_format($data['deficit'], 2);
        }

        // Format transaction details
        $transactionDetails = [];
        foreach ($data['transactions'] as $transaction) {
            $transactionDetails[] = "Order #{$transaction['order_id']}: $" . number_format($transaction['amount'], 2) . " ({$transaction['source']})";
        }
        $transactionDetailsString = implode('; ', $transactionDetails);

        // Write the row
        fputcsv($handle, [
            $data['gift_card_code'],
            count($data['transactions']),
            '$' . number_format($data['total_charges'], 2),
            $currentBalanceDisplay,
            $balanceStatus,
            $deficitAmount,
            $transactionDetailsString
        ]);
    }

    /**
     * Analyze cumulative charges for all gift cards
     */
    private function analyzeCumulativeCharges()
    {
        $this->info("🔍 Analyzing cumulative charges across all transactions...");
        $this->info("📅 Date filter: Transactions on or after July 1, 2025");

        // Reset cumulative charges array
        $this->cumulativeCharges = [];

        // Process discrepancy transactions (successful fixes)
        foreach ($this->discrepancies as $discrepancy) {
            $giftCardCode = $discrepancy['gift_card_code'] ?? 'Unknown';
            $orderId = $discrepancy['order_id'] ?? 'Unknown';
            $amount = $discrepancy['expected_amount'] ?? 0; // Use 'expected_amount' key

            // Only add if we have valid data
            if ($giftCardCode !== 'Unknown' && $orderId !== 'Unknown' && $amount > 0) {
                $this->addCumulativeCharge($giftCardCode, $orderId, $amount, 'discrepancy_fix');
            }
        }

        // Process existing database transactions
        $this->addExistingDatabaseTransactions();

        $this->info("✅ Cumulative charge analysis complete.");
        $this->info("📊 Found " . count($this->cumulativeCharges) . " unique gift cards with transaction history.");
    }

    /**
     * Add existing database transactions to cumulative analysis
     */
    private function addExistingDatabaseTransactions()
    {
        $this->info("🔍 Loading existing gift card transactions from database...");

        // Get all gift card transactions from database with gift card codes (filtered by date)
        $dbTransactions = DB::table('gift_card_transactions')
            ->join('gift_cards', 'gift_card_transactions.gift_card_id', '=', 'gift_cards.id')
            ->select('gift_cards.code as gift_card_code', 'gift_card_transactions.order_id', 'gift_card_transactions.amount', 'gift_card_transactions.type', 'gift_card_transactions.created_at')
            ->whereIn('gift_card_transactions.type', ['Order', 'Fix']) // Only include actual charges, not voids
            ->where('gift_card_transactions.created_at', '>=', $this->dateFilterFrom)
            ->get();

        foreach ($dbTransactions as $transaction) {
            // Validate transaction data before processing
            $giftCardCode = $transaction->gift_card_code ?? null;
            $orderId = $transaction->order_id ?? null;
            $amount = $transaction->amount ?? 0;
            $type = $transaction->type ?? 'unknown';
            $createdAt = $transaction->created_at ?? null;

            // Only add if we have valid data
            if ($giftCardCode && $orderId && $amount > 0) {
                $this->addCumulativeCharge(
                    $giftCardCode,
                    $orderId,
                    $amount,
                    'database_' . strtolower($type),
                    $createdAt
                );
            }
        }

        $this->info("✅ Loaded " . count($dbTransactions) . " existing database transactions.");
    }

    /**
     * Add a charge to cumulative tracking
     */
    private function addCumulativeCharge($giftCardCode, $orderId, $amount, $source, $timestamp = null)
    {
        // Validate input parameters
        if (empty($giftCardCode) || empty($orderId) || !is_numeric($amount) || $amount <= 0) {
            return; // Skip invalid data
        }

        if (!isset($this->cumulativeCharges[$giftCardCode])) {
            $this->cumulativeCharges[$giftCardCode] = [
                'gift_card_code' => $giftCardCode,
                'transactions' => [],
                'total_charges' => 0,
                'current_balance' => null,
                'balance_status' => 'unknown'
            ];
        }

        // Add transaction details
        $this->cumulativeCharges[$giftCardCode]['transactions'][] = [
            'order_id' => $orderId,
            'amount' => floatval($amount), // Ensure numeric value
            'source' => $source ?? 'unknown',
            'timestamp' => $timestamp ?: now()
        ];

        // Update total charges
        $this->cumulativeCharges[$giftCardCode]['total_charges'] += floatval($amount);
    }

    /**
     * Generate cumulative charge analysis report
     */
    private function generateCumulativeChargeReport()
    {
        if (empty($this->cumulativeCharges)) {
            $this->info("ℹ️  No cumulative charge data available for analysis.");
            return;
        }

        $this->newLine();
        $this->info("📊 CUMULATIVE CHARGE ANALYSIS");
        $this->info("Analyzing gift cards with multiple transactions...");

        // Get current balances for all gift cards
        $this->info("🔍 Retrieving current balances for cumulative analysis...");
        $progressBar = $this->output->createProgressBar(count($this->cumulativeCharges));
        $progressBar->start();

        foreach ($this->cumulativeCharges as $giftCardCode => &$data) {
            $currentBalance = $this->getGiftCardBalance($giftCardCode);
            $data['current_balance'] = $currentBalance;

            // Determine cumulative balance status
            if ($currentBalance !== 'Error' && is_numeric($currentBalance)) {
                if ($currentBalance >= $data['total_charges']) {
                    $data['balance_status'] = 'sufficient';
                } else {
                    $data['balance_status'] = 'insufficient';
                    $data['deficit'] = $data['total_charges'] - $currentBalance;
                }
            } else {
                $data['balance_status'] = 'unknown';
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Filter and display results
        $this->displayCumulativeChargeResults();

        // Export results to CSV
        $this->exportCumulativeChargeResults();
    }

    /**
     * Display cumulative charge analysis results
     */
    private function displayCumulativeChargeResults()
    {
        // Separate cards by status
        $multiChargeCards = array_filter($this->cumulativeCharges, function($data) {
            return count($data['transactions']) > 1;
        });

        $insufficientCards = array_filter($multiChargeCards, function($data) {
            return $data['balance_status'] === 'insufficient';
        });

        $sufficientCards = array_filter($multiChargeCards, function($data) {
            return $data['balance_status'] === 'sufficient';
        });

        $unknownCards = array_filter($multiChargeCards, function($data) {
            return $data['balance_status'] === 'unknown';
        });

        // Display summary
        $this->info("📈 MULTI-CHARGE GIFT CARD SUMMARY:");
        $this->table(
            ['Category', 'Count'],
            [
                ['Total Gift Cards Analyzed', count($this->cumulativeCharges)],
                ['Cards with Multiple Charges', count($multiChargeCards)],
                ['⚠️ Insufficient Cumulative Balance', count($insufficientCards)],
                ['✅ Sufficient Cumulative Balance', count($sufficientCards)],
                ['❓ Unknown Balance Status', count($unknownCards)]
            ]
        );

        // Display detailed insufficient balance cards
        if (!empty($insufficientCards)) {
            $this->newLine();
            $this->warn("⚠️ CRITICAL: Gift cards with cumulative charges exceeding current balance!");

            $insufficientTableData = [];
            foreach ($insufficientCards as $data) {
                $transactionList = [];
                foreach ($data['transactions'] as $transaction) {
                    $transactionList[] = "#{$transaction['order_id']} (\${$transaction['amount']})";
                }

                $currentBalanceDisplay = is_numeric($data['current_balance'])
                    ? '$' . number_format($data['current_balance'], 2)
                    : $data['current_balance'];

                $deficitDisplay = isset($data['deficit'])
                    ? '$' . number_format($data['deficit'], 2)
                    : 'N/A';

                $insufficientTableData[] = [
                    $data['gift_card_code'],
                    implode(', ', $transactionList),
                    '$' . number_format($data['total_charges'], 2),
                    $currentBalanceDisplay,
                    $deficitDisplay
                ];
            }

            $this->table(
                ['Gift Card Code', 'Transactions (Order#/Amount)', 'Total Charges', 'Current Balance', 'Deficit'],
                $insufficientTableData
            );

            $this->displayCumulativeChargeRecommendations($insufficientCards);
        }

        // Display multi-charge cards with sufficient balance (for reference)
        if (!empty($sufficientCards)) {
            $this->newLine();
            $this->info("✅ Multi-charge gift cards with sufficient balance:");

            $sufficientTableData = [];
            foreach (array_slice($sufficientCards, 0, 10) as $data) { // Show first 10 for brevity
                $transactionCount = count($data['transactions']);
                $currentBalanceDisplay = '$' . number_format($data['current_balance'], 2);

                $sufficientTableData[] = [
                    $data['gift_card_code'],
                    $transactionCount,
                    '$' . number_format($data['total_charges'], 2),
                    $currentBalanceDisplay
                ];
            }

            $this->table(
                ['Gift Card Code', 'Transaction Count', 'Total Charges', 'Current Balance'],
                $sufficientTableData
            );

            if (count($sufficientCards) > 10) {
                $remaining = count($sufficientCards) - 10;
                $this->info("... and {$remaining} more cards with sufficient balance.");
            }
        }
    }

    /**
     * Display recommendations for cumulative charge issues
     */
    private function displayCumulativeChargeRecommendations($insufficientCards)
    {
        $this->newLine();
        $this->warn("🔍 RECOMMENDED ACTIONS FOR CUMULATIVE OVERCHARGES:");
        $this->line("  1. 🔍 Investigate Transaction History:");
        $this->line("     • Review all transactions for each affected gift card");
        $this->line("     • Check for duplicate charges or processing errors");
        $this->line("     • Verify transaction timestamps and sources");

        $this->line("  2. 💰 Balance Reconciliation:");
        $this->line("     • Compare POS system records with database transactions");
        $this->line("     • Check for missing void/refund transactions");
        $this->line("     • Verify gift card recharge history");

        $this->line("  3. 🛠️ Corrective Actions:");
        $this->line("     • Consider partial refunds for overcharged amounts");
        $this->line("     • Add credit to affected gift cards if appropriate");
        $this->line("     • Update transaction records to reflect corrections");

        $this->line("  4. 🔒 Process Improvements:");
        $this->line("     • Implement real-time balance checking before charges");
        $this->line("     • Add cumulative charge validation to prevent future issues");
        $this->line("     • Review transaction processing workflows");

        $totalDeficit = array_sum(array_column($insufficientCards, 'deficit'));
        $this->newLine();
        $this->warn("💸 Total Deficit Across All Cards: $" . number_format($totalDeficit, 2));
        $this->warn("📊 Cards Affected: " . count($insufficientCards));
    }

    /**
     * Enhanced balance status that considers cumulative charges
     */
    private function getEnhancedBalanceStatus($giftCardCode, $currentTransactionAmount, $currentBalance)
    {
        // Check if we have cumulative data for this gift card
        if (isset($this->cumulativeCharges[$giftCardCode])) {
            $cumulativeData = $this->cumulativeCharges[$giftCardCode];

            // Use cumulative total instead of just current transaction
            $totalCharges = $cumulativeData['total_charges'];

            if ($currentBalance !== 'Error' && is_numeric($currentBalance)) {
                if ($currentBalance >= $totalCharges) {
                    return '✅ Sufficient (Cumulative)';
                } else {
                    return '⚠️ Insufficient (Cumulative)';
                }
            } else {
                return '❓ Unknown (Cumulative)';
            }
        }

        // Fall back to individual transaction logic
        if ($currentBalance !== 'Error' && is_numeric($currentBalance)) {
            if ($currentBalance >= $currentTransactionAmount) {
                return '✅ Sufficient';
            } else {
                return '⚠️ Insufficient';
            }
        } else {
            return '❓ Unknown';
        }
    }

    /**
     * Display balance status summary
     */
    private function displayBalanceStatusSummary($tableData)
    {
        $sufficient = 0;
        $insufficient = 0;
        $unknown = 0;
        $insufficientCards = [];

        foreach ($tableData as $row) {
            $balanceStatus = $row[4]; // Balance Status column

            if (strpos($balanceStatus, 'Sufficient') !== false) {
                $sufficient++;
            } elseif (strpos($balanceStatus, 'Insufficient') !== false) {
                $insufficient++;
                $insufficientCards[] = [
                    'code' => $row[0], // Gift Card Code
                    'order' => $row[1], // Order ID
                    'charged' => $row[2], // Amount Charged
                    'balance' => $row[3] // Current Balance
                ];
            } else {
                $unknown++;
            }
        }

        $this->newLine();
        $this->info("💰 BALANCE STATUS SUMMARY:");

        $this->table(
            ['Status', 'Count'],
            [
                ['✅ Sufficient Funds', $sufficient],
                ['⚠️ Insufficient Funds', $insufficient],
                ['❓ Unknown/Error', $unknown]
            ]
        );

        // Highlight insufficient balance issues
        if ($insufficient > 0) {
            $this->newLine();
            $this->warn("⚠️ ATTENTION: {$insufficient} gift card(s) have insufficient balance!");
            $this->warn("This may indicate data inconsistencies or processing errors:");

            foreach ($insufficientCards as $card) {
                $this->line("  • {$card['code']} (Order: {$card['order']}) - Charged: {$card['charged']}, Balance: {$card['balance']}");
            }

            $this->newLine();
            $this->warn("🔍 Recommended Actions:");
            $this->line("  1. Verify these transactions in the POS system");
            $this->line("  2. Check if gift cards were recharged after the original transaction");
            $this->line("  3. Investigate potential data synchronization issues");
            $this->line("  4. Consider manual reconciliation for affected cards");
        }
    }
}
