<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order;
use App\Product;
use Carbon\Carbon;

class GenerateQuantityMismatchReport extends Command
{
    protected $signature = 'report:quantity-mismatch {date=2025-09-08}';
    protected $description = 'Generate CSV report for quantity mismatches in subscription orders';

    public function handle()
    {
        $date = $this->argument('date');
        $filename = "quantity_mismatch_report_" . Carbon::parse($date)->format('M_d_Y') . ".csv";
        $filepath = storage_path("app/{$filename}");

        // Get orders for the specified date with recurring_id not null
        $orders = Order::whereDate('created_at', $date)
            ->whereNotNull('recurring_id')
            ->get();

        $this->info("Found {$orders->count()} subscription orders for {$date}");

        $csvData = [];
        $csvData[] = ['order_id', 'product_sku', 'quantity', 'store_deduct'];

        foreach ($orders as $order) {
            if (!isset($order->products) || !is_array($order->products)) {
                continue;
            }

            foreach ($order->products as $orderProduct) {
                $productId = $orderProduct['product_id'] ?? null;
                $quantity = $orderProduct['quantity'] ?? 0;
                $storeDeduct = $orderProduct['store_deduct'] ?? 0;

                // Only include if quantity != store_deduct
                if ($quantity != $storeDeduct) {
                    $csvData[] = [
                        $order->id,
                        $orderProduct['sku'],
                        $quantity,
                        $storeDeduct
                    ];
                }
            }
        }

        // Write CSV file
        $file = fopen($filepath, 'w');
        foreach ($csvData as $row) {
            fputcsv($file, $row);
        }
        fclose($file);

        $recordCount = count($csvData) - 1; // Subtract header row
        $this->info("Report generated: {$filename}");
        $this->info("Found {$recordCount} quantity mismatches");

        return 0;
    }
}