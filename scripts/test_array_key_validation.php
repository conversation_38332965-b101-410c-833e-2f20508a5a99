<?php

/**
 * Test script to verify array key validation fixes
 * Tests the handling of missing or invalid array keys in cumulative analysis
 */

echo "🧪 Testing Array Key Validation Fixes\n";
echo "====================================\n\n";

// Test data structures that might cause "Undefined array key" errors
$testDiscrepancies = [
    // Valid discrepancy with correct keys
    [
        'gift_card_code' => 'E1234567890123456',
        'order_id' => '1001',
        'expected_amount' => 25.00,
        'pos_charge_amount' => 0,
        'transaction_id' => 'T001'
    ],
    // Missing 'expected_amount' key (should be handled gracefully)
    [
        'gift_card_code' => 'E2345678901234567',
        'order_id' => '1002',
        'pos_charge_amount' => 0,
        'transaction_id' => 'T002'
        // 'expected_amount' is missing
    ],
    // Missing 'gift_card_code' key
    [
        'order_id' => '1003',
        'expected_amount' => 30.00,
        'pos_charge_amount' => 0,
        'transaction_id' => 'T003'
        // 'gift_card_code' is missing
    ],
    // Invalid amount (zero)
    [
        'gift_card_code' => 'E3456789012345678',
        'order_id' => '1004',
        'expected_amount' => 0,
        'pos_charge_amount' => 0,
        'transaction_id' => 'T004'
    ],
    // Invalid amount (negative)
    [
        'gift_card_code' => 'E4567890123456789',
        'order_id' => '1005',
        'expected_amount' => -10.00,
        'pos_charge_amount' => 0,
        'transaction_id' => 'T005'
    ],
    // Non-numeric amount
    [
        'gift_card_code' => 'E5678901234567890',
        'order_id' => '1006',
        'expected_amount' => 'invalid',
        'pos_charge_amount' => 0,
        'transaction_id' => 'T006'
    ]
];

$testDatabaseTransactions = [
    // Valid transaction
    (object)[
        'gift_card_code' => 'E1234567890123456',
        'order_id' => '2001',
        'amount' => 15.00,
        'type' => 'Order',
        'created_at' => '2024-01-01 10:00:00'
    ],
    // Missing 'amount' key
    (object)[
        'gift_card_code' => 'E2345678901234567',
        'order_id' => '2002',
        'type' => 'Order',
        'created_at' => '2024-01-01 11:00:00'
        // 'amount' is missing
    ],
    // Missing 'gift_card_code' key
    (object)[
        'order_id' => '2003',
        'amount' => 20.00,
        'type' => 'Order',
        'created_at' => '2024-01-01 12:00:00'
        // 'gift_card_code' is missing
    ]
];

$testSuccessResults = [
    // Valid success result
    [
        'gift_card_code' => 'E1234567890123456',
        'order_id' => '3001',
        'amount' => 25.00,
        'result' => ['status' => 'success'],
        'success' => true
    ],
    // Missing 'amount' key
    [
        'gift_card_code' => 'E2345678901234567',
        'order_id' => '3002',
        'result' => ['status' => 'success'],
        'success' => true
        // 'amount' is missing
    ],
    // Missing 'gift_card_code' key
    [
        'order_id' => '3003',
        'amount' => 30.00,
        'result' => ['status' => 'success'],
        'success' => true
        // 'gift_card_code' is missing
    ]
];

// Mock cumulative charges array
$cumulativeCharges = [];

// Test function to simulate addCumulativeCharge with validation
function testAddCumulativeCharge(&$cumulativeCharges, $giftCardCode, $orderId, $amount, $source, $timestamp = null)
{
    // Validate input parameters (same as in the fixed code)
    if (empty($giftCardCode) || empty($orderId) || !is_numeric($amount) || $amount <= 0) {
        return false; // Skip invalid data
    }

    if (!isset($cumulativeCharges[$giftCardCode])) {
        $cumulativeCharges[$giftCardCode] = [
            'gift_card_code' => $giftCardCode,
            'transactions' => [],
            'total_charges' => 0,
            'current_balance' => null,
            'balance_status' => 'unknown'
        ];
    }

    // Add transaction details
    $cumulativeCharges[$giftCardCode]['transactions'][] = [
        'order_id' => $orderId,
        'amount' => floatval($amount),
        'source' => $source ?? 'unknown',
        'timestamp' => $timestamp ?: date('Y-m-d H:i:s')
    ];

    // Update total charges
    $cumulativeCharges[$giftCardCode]['total_charges'] += floatval($amount);
    
    return true;
}

echo "1. Testing Discrepancy Processing:\n";
echo str_repeat("-", 40) . "\n";

$validDiscrepancies = 0;
$skippedDiscrepancies = 0;

foreach ($testDiscrepancies as $index => $discrepancy) {
    echo "Discrepancy " . ($index + 1) . ": ";
    
    $giftCardCode = $discrepancy['gift_card_code'] ?? 'Unknown';
    $orderId = $discrepancy['order_id'] ?? 'Unknown';
    $amount = $discrepancy['expected_amount'] ?? 0;
    
    if ($giftCardCode !== 'Unknown' && $orderId !== 'Unknown' && $amount > 0) {
        $success = testAddCumulativeCharge($cumulativeCharges, $giftCardCode, $orderId, $amount, 'discrepancy_fix');
        if ($success) {
            echo "✅ Processed - {$giftCardCode}, Order: {$orderId}, Amount: \${$amount}\n";
            $validDiscrepancies++;
        } else {
            echo "⚠️ Validation failed\n";
            $skippedDiscrepancies++;
        }
    } else {
        echo "⚠️ Skipped - Missing or invalid data\n";
        $skippedDiscrepancies++;
    }
}

echo "\n2. Testing Database Transaction Processing:\n";
echo str_repeat("-", 40) . "\n";

$validTransactions = 0;
$skippedTransactions = 0;

foreach ($testDatabaseTransactions as $index => $transaction) {
    echo "Transaction " . ($index + 1) . ": ";
    
    $giftCardCode = $transaction->gift_card_code ?? null;
    $orderId = $transaction->order_id ?? null;
    $amount = $transaction->amount ?? 0;
    $type = $transaction->type ?? 'unknown';
    $createdAt = $transaction->created_at ?? null;
    
    if ($giftCardCode && $orderId && $amount > 0) {
        $success = testAddCumulativeCharge($cumulativeCharges, $giftCardCode, $orderId, $amount, 'database_' . strtolower($type), $createdAt);
        if ($success) {
            echo "✅ Processed - {$giftCardCode}, Order: {$orderId}, Amount: \${$amount}\n";
            $validTransactions++;
        } else {
            echo "⚠️ Validation failed\n";
            $skippedTransactions++;
        }
    } else {
        echo "⚠️ Skipped - Missing or invalid data\n";
        $skippedTransactions++;
    }
}

echo "\n3. Testing Success Result Processing:\n";
echo str_repeat("-", 40) . "\n";

$validResults = 0;
$skippedResults = 0;

foreach ($testSuccessResults as $index => $success) {
    echo "Result " . ($index + 1) . ": ";
    
    $giftCardCode = $success['gift_card_code'] ?? 'Unknown';
    $orderId = $success['order_id'] ?? 'Unknown';
    $amount = $success['amount'] ?? 0;
    $resultStatus = $success['result']['status'] ?? 'unknown';
    
    if ($giftCardCode === 'Unknown' || $orderId === 'Unknown' || $amount <= 0) {
        echo "⚠️ Skipped - Missing or invalid data\n";
        $skippedResults++;
    } else {
        echo "✅ Valid - {$giftCardCode}, Order: {$orderId}, Amount: \${$amount}, Status: {$resultStatus}\n";
        $validResults++;
    }
}

echo "\n📊 VALIDATION TEST RESULTS:\n";
echo str_repeat("=", 40) . "\n";
echo "Discrepancies:\n";
echo "  ✅ Valid: {$validDiscrepancies}\n";
echo "  ⚠️ Skipped: {$skippedDiscrepancies}\n";
echo "\nDatabase Transactions:\n";
echo "  ✅ Valid: {$validTransactions}\n";
echo "  ⚠️ Skipped: {$skippedTransactions}\n";
echo "\nSuccess Results:\n";
echo "  ✅ Valid: {$validResults}\n";
echo "  ⚠️ Skipped: {$skippedResults}\n";

echo "\n📋 CUMULATIVE CHARGES SUMMARY:\n";
echo str_repeat("=", 40) . "\n";
echo "Gift Cards Processed: " . count($cumulativeCharges) . "\n";

foreach ($cumulativeCharges as $giftCardCode => $data) {
    echo "\n{$giftCardCode}:\n";
    echo "  Total Charges: \${$data['total_charges']}\n";
    echo "  Transactions: " . count($data['transactions']) . "\n";
    foreach ($data['transactions'] as $transaction) {
        echo "    - Order #{$transaction['order_id']}: \${$transaction['amount']} ({$transaction['source']})\n";
    }
}

echo "\n🎉 Array key validation test completed successfully!\n";
echo "💡 The fixes should prevent 'Undefined array key' errors by:\n";
echo "  • Using null coalescing operator (??) for safe key access\n";
echo "  • Validating data before processing\n";
echo "  • Skipping invalid or incomplete records\n";
echo "  • Providing default values for missing keys\n";
