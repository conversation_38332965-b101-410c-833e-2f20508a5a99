<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\GiftCardController;
use Illuminate\Http\Request;
use GuzzleHttp\Client;

/**
 * Standalone script to analyze cumulative gift card charges
 * Identifies gift cards where total charges exceed current balance
 */

class CumulativeChargeAnalyzer
{
    private $cumulativeCharges = [];
    private $balanceCache = [];
    private $dateFilterFrom = '2025-07-01 00:00:00';

    public function __construct()
    {
        $this->loadBalanceCache();
    }

    /**
     * Main analysis function
     */
    public function analyze()
    {
        echo "🔍 CUMULATIVE GIFT CARD CHARGE ANALYSIS\n";
        echo "=====================================\n\n";

        $this->loadDiscrepancyData();
        $this->loadDatabaseTransactions();
        $this->retrieveCurrentBalances();
        $this->generateReport();
    }

    /**
     * Load discrepancy data from CSV
     */
    private function loadDiscrepancyData()
    {
        echo "📄 Loading POS discrepancy data...\n";
        
        $csvFile = __DIR__ . '/../eichlersapi.csv';
        if (!file_exists($csvFile)) {
            echo "❌ CSV file not found: {$csvFile}\n";
            return;
        }

        $handle = fopen($csvFile, 'r');
        if (!$handle) {
            echo "❌ Could not open CSV file\n";
            return;
        }

        // Skip header
        $header = fgetcsv($handle);
        
        $discrepancyCount = 0;
        while (($row = fgetcsv($handle)) !== false) {
            if (count($row) >= 4) {
                $chargeAmount = floatval($row[3]); // ChargeAmount column
                
                // Only process transactions with ChargeAmount = 0 (discrepancies)
                if ($chargeAmount == 0) {
                    $paymentNumber = trim($row[2]); // PaymentNumber column
                    
                    // Extract gift card and order info from PaymentNumber
                    if (preg_match('/^(\d{4})_(\d{4})$/', $paymentNumber, $matches)) {
                        $giftCardLast4 = $matches[1];
                        $orderLast4 = $matches[2];
                        
                        // Find matching database transaction
                        $dbTransaction = $this->findMatchingTransaction($giftCardLast4, $orderLast4);
                        if ($dbTransaction) {
                            $this->addCumulativeCharge(
                                $dbTransaction->gift_card_code,
                                $dbTransaction->order_id,
                                $dbTransaction->amount,
                                'discrepancy_fix'
                            );
                            $discrepancyCount++;
                        }
                    }
                }
            }
        }
        
        fclose($handle);
        echo "✅ Processed {$discrepancyCount} discrepancy transactions\n";
    }

    /**
     * Find matching database transaction
     */
    private function findMatchingTransaction($giftCardLast4, $orderLast4)
    {
        return DB::table('gift_card_transactions')
            ->join('orders', 'gift_card_transactions.order_id', '=', 'orders.id')
            ->where('gift_card_transactions.type', 'Order')
            ->where('gift_card_transactions.amount', '>', 0)
            ->whereRaw('RIGHT(gift_card_transactions.gift_card_code, 4) = ?', [$giftCardLast4])
            ->whereRaw('RIGHT(orders.id, 4) = ?', [$orderLast4])
            ->select('gift_card_transactions.*')
            ->first();
    }

    /**
     * Load all database transactions
     */
    private function loadDatabaseTransactions()
    {
        echo "🗄️  Loading database transactions...\n";
        echo "📅 Date filter: Transactions on or after July 1, 2025\n";

        $transactions = DB::table('gift_card_transactions')
            ->join('gift_cards', 'gift_card_transactions.gift_card_id', '=', 'gift_cards.id')
            ->select('gift_cards.code as gift_card_code', 'gift_card_transactions.order_id', 'gift_card_transactions.amount', 'gift_card_transactions.type', 'gift_card_transactions.created_at')
            ->whereIn('gift_card_transactions.type', ['Order', 'Fix'])
            ->where('gift_card_transactions.amount', '>', 0)
            ->where('gift_card_transactions.created_at', '>=', $this->dateFilterFrom)
            ->get();

        foreach ($transactions as $transaction) {
            // Validate transaction data before processing
            $giftCardCode = $transaction->gift_card_code ?? null;
            $orderId = $transaction->order_id ?? null;
            $amount = $transaction->amount ?? 0;
            $type = $transaction->type ?? 'unknown';
            $createdAt = $transaction->created_at ?? null;

            // Only add if we have valid data
            if ($giftCardCode && $orderId && $amount > 0) {
                $this->addCumulativeCharge(
                    $giftCardCode,
                    $orderId,
                    $amount,
                    'database_' . strtolower($type),
                    $createdAt
                );
            }
        }

        echo "✅ Loaded " . count($transactions) . " database transactions\n";
    }

    /**
     * Add charge to cumulative tracking
     */
    private function addCumulativeCharge($giftCardCode, $orderId, $amount, $source, $timestamp = null)
    {
        // Validate input parameters
        if (empty($giftCardCode) || empty($orderId) || !is_numeric($amount) || $amount <= 0) {
            return; // Skip invalid data
        }

        if (!isset($this->cumulativeCharges[$giftCardCode])) {
            $this->cumulativeCharges[$giftCardCode] = [
                'gift_card_code' => $giftCardCode,
                'transactions' => [],
                'total_charges' => 0,
                'current_balance' => null,
                'balance_status' => 'unknown'
            ];
        }

        $this->cumulativeCharges[$giftCardCode]['transactions'][] = [
            'order_id' => $orderId,
            'amount' => floatval($amount), // Ensure numeric value
            'source' => $source ?? 'unknown',
            'timestamp' => $timestamp ?: date('Y-m-d H:i:s')
        ];

        $this->cumulativeCharges[$giftCardCode]['total_charges'] += floatval($amount);
    }

    /**
     * Retrieve current balances from POS
     */
    private function retrieveCurrentBalances()
    {
        echo "💰 Retrieving current balances from POS...\n";
        
        $total = count($this->cumulativeCharges);
        $current = 0;
        
        foreach ($this->cumulativeCharges as $giftCardCode => &$data) {
            $current++;
            echo "  Progress: {$current}/{$total} - {$giftCardCode}\r";
            
            $balance = $this->getGiftCardBalance($giftCardCode);
            $data['current_balance'] = $balance;
            
            // Determine status
            if ($balance !== 'Error' && is_numeric($balance)) {
                if ($balance >= $data['total_charges']) {
                    $data['balance_status'] = 'sufficient';
                } else {
                    $data['balance_status'] = 'insufficient';
                    $data['deficit'] = $data['total_charges'] - $balance;
                }
            } else {
                $data['balance_status'] = 'unknown';
            }
            
            // Small delay to avoid rate limiting
            usleep(250000);
        }
        
        echo "\n✅ Balance retrieval complete\n";
    }

    /**
     * Get gift card balance with caching
     */
    private function getGiftCardBalance($giftCardCode)
    {
        if (isset($this->balanceCache[$giftCardCode])) {
            return $this->balanceCache[$giftCardCode];
        }

        try {
            $controller = new GiftCardController();
            $request = new Request(['code' => $giftCardCode]);
            $balance = $controller->checkGiftCard($request);
            
            $this->balanceCache[$giftCardCode] = $balance;
            $this->saveBalanceCache();
            
            return $balance;
        } catch (Exception $e) {
            $this->balanceCache[$giftCardCode] = 'Error';
            $this->saveBalanceCache();
            return 'Error';
        }
    }

    /**
     * Load balance cache
     */
    private function loadBalanceCache()
    {
        $cacheFile = __DIR__ . '/../storage/app/gift_card_balance_cache.json';
        if (file_exists($cacheFile)) {
            $this->balanceCache = json_decode(file_get_contents($cacheFile), true) ?: [];
        }
    }

    /**
     * Save balance cache
     */
    private function saveBalanceCache()
    {
        $cacheFile = __DIR__ . '/../storage/app/gift_card_balance_cache.json';
        file_put_contents($cacheFile, json_encode($this->balanceCache, JSON_PRETTY_PRINT));
    }

    /**
     * Generate comprehensive report
     */
    private function generateReport()
    {
        echo "\n📊 CUMULATIVE CHARGE ANALYSIS RESULTS\n";
        echo "====================================\n\n";

        $multiChargeCards = array_filter($this->cumulativeCharges, function($data) {
            return count($data['transactions']) > 1;
        });

        $insufficientCards = array_filter($multiChargeCards, function($data) {
            return $data['balance_status'] === 'insufficient';
        });

        echo "📈 SUMMARY:\n";
        echo "  Total Gift Cards: " . count($this->cumulativeCharges) . "\n";
        echo "  Multi-Charge Cards: " . count($multiChargeCards) . "\n";
        echo "  ⚠️  Insufficient Balance: " . count($insufficientCards) . "\n\n";

        if (!empty($insufficientCards)) {
            echo "⚠️  CRITICAL ISSUES - Cumulative charges exceed balance:\n";
            echo str_repeat("=", 80) . "\n";
            
            $totalDeficit = 0;
            foreach ($insufficientCards as $data) {
                echo "Gift Card: {$data['gift_card_code']}\n";
                echo "  Current Balance: $" . number_format($data['current_balance'], 2) . "\n";
                echo "  Total Charges: $" . number_format($data['total_charges'], 2) . "\n";
                echo "  Deficit: $" . number_format($data['deficit'], 2) . "\n";
                echo "  Transactions:\n";
                
                foreach ($data['transactions'] as $transaction) {
                    echo "    - Order #{$transaction['order_id']}: $" . number_format($transaction['amount'], 2) . " ({$transaction['source']})\n";
                }
                echo "\n";
                
                $totalDeficit += $data['deficit'];
            }
            
            echo "💸 TOTAL DEFICIT: $" . number_format($totalDeficit, 2) . "\n";
            echo "📊 AFFECTED CARDS: " . count($insufficientCards) . "\n\n";
            
            echo "🔍 RECOMMENDED ACTIONS:\n";
            echo "  1. Review transaction history for each affected card\n";
            echo "  2. Investigate potential duplicate charges\n";
            echo "  3. Check for missing void/refund transactions\n";
            echo "  4. Consider partial refunds for overcharged amounts\n";
            echo "  5. Implement real-time balance validation\n";
        } else {
            echo "✅ No cumulative balance issues found!\n";
        }
    }
}

// Run the analysis
$analyzer = new CumulativeChargeAnalyzer();
$analyzer->analyze();
