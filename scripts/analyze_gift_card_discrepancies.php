<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\GiftCard;
use App\GiftCardTransaction;
use App\Order;

/**
 * Gift Card Transaction Discrepancy Analysis Script
 * 
 * This script analyzes discrepancies between POS system (eichlersapi.csv) 
 * and database gift card transactions where:
 * - POS shows ChargeAmount = 0 with successful response (200)
 * - Database shows actual charge amounts > 0 for type = 'Order'
 */

class GiftCardDiscrepancyAnalyzer
{
    private $csvFile;
    private $discrepancies = [];
    private $posTransactions = [];
    private $dbTransactions = [];

    public function __construct($csvFile = 'eichlersapi.csv')
    {
        $this->csvFile = $csvFile;
    }

    /**
     * Main analysis method
     */
    public function analyze()
    {
        echo "Starting Gift Card Transaction Discrepancy Analysis...\n\n";

        // Step 1: Parse POS data
        $this->parsePosData();
        echo "Found " . count($this->posTransactions) . " successful POS transactions with ChargeAmount = 0\n";

        // Step 2: Query database transactions
        $this->queryDatabaseTransactions();
        echo "Found " . count($this->dbTransactions) . " database transactions with type='Order' and amount > 0\n";

        // Step 3: Match records
        $this->matchRecords();
        echo "Found " . count($this->discrepancies) . " discrepancies\n\n";

        // Step 4: Generate report
        $this->generateReport();

        return $this->discrepancies;
    }

    /**
     * Parse POS CSV data to find successful transactions with ChargeAmount = 0
     */
    private function parsePosData()
    {
        if (!file_exists($this->csvFile)) {
            throw new Exception("CSV file not found: {$this->csvFile}");
        }

        $handle = fopen($this->csvFile, 'r');
        $header = fgetcsv($handle); // Skip header row

        while (($row = fgetcsv($handle)) !== false) {
            $data = array_combine($header, $row);
            
            // Parse the JSON data field
            $jsonData = json_decode($data['data'], true);
            
            // Filter for successful transactions with ChargeAmount = 0
            if ($data['ResponseCode'] == '200' && 
                isset($jsonData['ChargeAmount']) && 
                $jsonData['ChargeAmount'] == 0) {
                
                $this->posTransactions[] = [
                    'voucher_number' => $jsonData['VoucherNumber'],
                    'payment_number' => $jsonData['PaymentNumber'],
                    'charge_amount' => $jsonData['ChargeAmount'],
                    'response_code' => $data['ResponseCode'],
                    'created_on' => $data['CreatedOn'],
                    'message' => $data['Message']
                ];
            }
        }
        fclose($handle);
    }

    /**
     * Query database for gift card transactions with type='Order' and amount > 0
     */
    private function queryDatabaseTransactions()
    {
        $this->dbTransactions = DB::table('gift_card_transactions as gct')
            ->join('gift_cards as gc', 'gct.gift_card_id', '=', 'gc.id')
            ->join('orders as o', 'gct.order_id', '=', 'o.id')
            ->where('gct.type', 'Order')
            ->where('gct.amount', '>', 0)
            ->select([
                'gct.id as transaction_id',
                'gct.order_id',
                'gct.gift_card_id',
                'gct.amount',
                'gct.type',
                'gct.created_at',
                'gc.code as gift_card_code',
                'o.id as order_id'
            ])
            ->get()
            ->toArray();
    }

    /**
     * Match POS transactions with database transactions using PaymentNumber format
     * PaymentNumber format: {last_4_digits_of_gift_card_code}_{last_4_digits_of_order_id}
     */
    private function matchRecords()
    {
        foreach ($this->posTransactions as $posTransaction) {
            $paymentNumber = $posTransaction['payment_number'];
            
            // Skip VOID transactions
            if (strpos($paymentNumber, 'VOID') !== false) {
                continue;
            }

            // Parse payment number: {gift_card_last_4}_{order_last_4}
            $parts = explode('_', $paymentNumber);
            if (count($parts) !== 2) {
                continue;
            }

            $giftCardLast4 = $parts[0];
            $orderLast4 = $parts[1];

            // Find matching database transaction
            foreach ($this->dbTransactions as $dbTransaction) {
                $dbGiftCardLast4 = substr($dbTransaction->gift_card_code, -4);
                $dbOrderLast4 = substr($dbTransaction->order_id, -4);

                if ($dbGiftCardLast4 === $giftCardLast4 && $dbOrderLast4 === $orderLast4) {
                    // Found a match - this is a discrepancy
                    $this->discrepancies[] = [
                        'gift_card_code' => $dbTransaction->gift_card_code,
                        'order_id' => $dbTransaction->order_id,
                        'expected_amount' => $dbTransaction->amount,
                        'pos_charge_amount' => $posTransaction['charge_amount'],
                        'pos_voucher_number' => $posTransaction['voucher_number'],
                        'pos_payment_number' => $posTransaction['payment_number'],
                        'transaction_id' => $dbTransaction->transaction_id,
                        'gift_card_id' => $dbTransaction->gift_card_id,
                        'pos_created_on' => $posTransaction['created_on'],
                        'db_created_at' => $dbTransaction->created_at
                    ];
                    break;
                }
            }
        }
    }

    /**
     * Generate detailed discrepancy report
     */
    private function generateReport()
    {
        echo "=== GIFT CARD TRANSACTION DISCREPANCY REPORT ===\n\n";
        
        if (empty($this->discrepancies)) {
            echo "No discrepancies found.\n";
            return;
        }

        $totalDiscrepancyAmount = 0;
        
        foreach ($this->discrepancies as $index => $discrepancy) {
            echo "Discrepancy #" . ($index + 1) . ":\n";
            echo "  Gift Card Code: {$discrepancy['gift_card_code']}\n";
            echo "  Order ID: {$discrepancy['order_id']}\n";
            echo "  Expected Amount: \${$discrepancy['expected_amount']}\n";
            echo "  POS Charge Amount: \${$discrepancy['pos_charge_amount']}\n";
            echo "  POS Voucher Number: {$discrepancy['pos_voucher_number']}\n";
            echo "  POS Payment Number: {$discrepancy['pos_payment_number']}\n";
            echo "  Transaction ID: {$discrepancy['transaction_id']}\n";
            echo "  Gift Card ID: {$discrepancy['gift_card_id']}\n";
            echo "  POS Created: {$discrepancy['pos_created_on']}\n";
            echo "  DB Created: {$discrepancy['db_created_at']}\n";
            echo "  ---\n";
            
            $totalDiscrepancyAmount += $discrepancy['expected_amount'];
        }
        
        echo "\nSUMMARY:\n";
        echo "Total Discrepancies: " . count($this->discrepancies) . "\n";
        echo "Total Amount Not Charged in POS: \${$totalDiscrepancyAmount}\n\n";
        
        // Save detailed report to file
        $this->saveReportToFile();
    }

    /**
     * Save detailed report to CSV file
     */
    private function saveReportToFile()
    {
        $filename = 'gift_card_discrepancy_report_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = __DIR__ . '/' . $filename;
        
        $handle = fopen($filepath, 'w');
        
        // Write header
        fputcsv($handle, [
            'Gift Card Code',
            'Order ID', 
            'Expected Amount',
            'POS Charge Amount',
            'POS Voucher Number',
            'POS Payment Number',
            'Transaction ID',
            'Gift Card ID',
            'POS Created',
            'DB Created'
        ]);
        
        // Write data
        foreach ($this->discrepancies as $discrepancy) {
            fputcsv($handle, [
                $discrepancy['gift_card_code'],
                $discrepancy['order_id'],
                $discrepancy['expected_amount'],
                $discrepancy['pos_charge_amount'],
                $discrepancy['pos_voucher_number'],
                $discrepancy['pos_payment_number'],
                $discrepancy['transaction_id'],
                $discrepancy['gift_card_id'],
                $discrepancy['pos_created_on'],
                $discrepancy['db_created_at']
            ]);
        }
        
        fclose($handle);
        echo "Detailed report saved to: {$filepath}\n";
    }

    /**
     * Get discrepancies for further processing
     */
    public function getDiscrepancies()
    {
        return $this->discrepancies;
    }
}

// Run the analysis if script is executed directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $analyzer = new GiftCardDiscrepancyAnalyzer();
        $discrepancies = $analyzer->analyze();
        
        echo "\nAnalysis complete. Use the discrepancies data to implement fixes.\n";
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}
