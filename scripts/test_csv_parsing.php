<?php

/**
 * Test script to validate CSV parsing and data structure
 * Run this first to ensure the CSV file is properly formatted
 */

$csvFile = 'eichlersapi.csv';

if (!file_exists($csvFile)) {
    echo "❌ CSV file not found: {$csvFile}\n";
    echo "Please ensure the POS export file is in the current directory.\n";
    exit(1);
}

echo "🔍 Testing CSV file parsing...\n\n";

$handle = fopen($csvFile, 'r');
$header = fgetcsv($handle);

echo "📋 CSV Headers:\n";
foreach ($header as $index => $column) {
    echo "  [{$index}] {$column}\n";
}
echo "\n";

// Parse first few rows to validate structure
$rowCount = 0;
$successfulZeroChargeCount = 0;
$successfulNonZeroChargeCount = 0;
$errorCount = 0;
$sampleTransactions = [];

while (($row = fgetcsv($handle)) !== false && $rowCount < 20) {
    $data = array_combine($header, $row);
    $rowCount++;
    
    // Parse JSON data
    $jsonData = json_decode($data['data'], true);
    
    if ($jsonData === null) {
        echo "⚠️  Row {$rowCount}: Invalid JSON in data field\n";
        continue;
    }
    
    $responseCode = $data['ResponseCode'];
    $chargeAmount = $jsonData['ChargeAmount'] ?? 'N/A';
    $paymentNumber = $jsonData['PaymentNumber'] ?? 'N/A';
    $voucherNumber = $jsonData['VoucherNumber'] ?? 'N/A';
    
    if ($responseCode == '200') {
        if ($chargeAmount == 0) {
            $successfulZeroChargeCount++;
            if (count($sampleTransactions) < 3) {
                $sampleTransactions[] = [
                    'row' => $rowCount,
                    'voucher' => $voucherNumber,
                    'payment' => $paymentNumber,
                    'charge' => $chargeAmount,
                    'response' => $responseCode
                ];
            }
        } else {
            $successfulNonZeroChargeCount++;
        }
    } else {
        $errorCount++;
    }
}

fclose($handle);

echo "📊 Sample Analysis (first 20 rows):\n";
echo "  Total rows processed: {$rowCount}\n";
echo "  Successful transactions with ChargeAmount = 0: {$successfulZeroChargeCount}\n";
echo "  Successful transactions with ChargeAmount > 0: {$successfulNonZeroChargeCount}\n";
echo "  Error transactions: {$errorCount}\n\n";

if (!empty($sampleTransactions)) {
    echo "🔍 Sample Zero-Charge Transactions:\n";
    foreach ($sampleTransactions as $sample) {
        echo "  Row {$sample['row']}:\n";
        echo "    Voucher: {$sample['voucher']}\n";
        echo "    Payment: {$sample['payment']}\n";
        echo "    Charge: \${$sample['charge']}\n";
        echo "    Response: {$sample['response']}\n";
        
        // Test payment number parsing
        $parts = explode('_', $sample['payment']);
        if (count($parts) === 2 && !strpos($sample['payment'], 'VOID')) {
            echo "    ✅ Payment number format valid: {$parts[0]} (gift card) + {$parts[1]} (order)\n";
        } else {
            echo "    ⚠️  Payment number format unusual\n";
        }
        echo "\n";
    }
}

// Now test full file parsing
echo "🔄 Testing full file parsing...\n";

$handle = fopen($csvFile, 'r');
$header = fgetcsv($handle);
$totalRows = 0;
$totalZeroCharge = 0;
$validPaymentNumbers = 0;

while (($row = fgetcsv($handle)) !== false) {
    $data = array_combine($header, $row);
    $totalRows++;
    
    $jsonData = json_decode($data['data'], true);
    
    if ($data['ResponseCode'] == '200' && 
        isset($jsonData['ChargeAmount']) && 
        $jsonData['ChargeAmount'] == 0) {
        
        $totalZeroCharge++;
        
        $paymentNumber = $jsonData['PaymentNumber'];
        if (!strpos($paymentNumber, 'VOID')) {
            $parts = explode('_', $paymentNumber);
            if (count($parts) === 2) {
                $validPaymentNumbers++;
            }
        }
    }
}

fclose($handle);

echo "📊 Full File Analysis:\n";
echo "  Total rows: {$totalRows}\n";
echo "  Zero-charge successful transactions: {$totalZeroCharge}\n";
echo "  Valid payment number format: {$validPaymentNumbers}\n";
echo "  Potential discrepancies to check: {$validPaymentNumbers}\n\n";

if ($validPaymentNumbers > 0) {
    echo "✅ CSV file appears valid and contains potential discrepancies to analyze.\n";
    echo "💡 Next step: Run the full analysis script\n";
    echo "   php scripts/analyze_gift_card_discrepancies.php\n";
    echo "   OR\n";
    echo "   php artisan giftcard:fix-discrepancies analyze\n";
} else {
    echo "ℹ️  No potential discrepancies found in CSV file.\n";
    echo "   This could mean:\n";
    echo "   - All transactions are properly synchronized\n";
    echo "   - The CSV file doesn't contain the expected data\n";
    echo "   - The data format has changed\n";
}

echo "\n✅ CSV parsing test complete.\n";
