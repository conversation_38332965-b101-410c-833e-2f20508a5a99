<?php

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/analyze_gift_card_discrepancies.php';
require_once __DIR__ . '/fix_gift_card_discrepancies.php';

/**
 * Gift Card Discrepancy Manager
 * 
 * Command-line interface for analyzing and fixing gift card transaction discrepancies
 * 
 * Usage:
 *   php gift_card_discrepancy_manager.php analyze
 *   php gift_card_discrepancy_manager.php fix --dry-run
 *   php gift_card_discrepancy_manager.php fix --live
 *   php gift_card_discrepancy_manager.php both --dry-run
 */

class GiftCardDiscrepancyManager
{
    private $command;
    private $options;

    public function __construct($argv)
    {
        $this->parseArguments($argv);
    }

    private function parseArguments($argv)
    {
        $this->command = $argv[1] ?? 'help';
        $this->options = array_slice($argv, 2);
    }

    public function run()
    {
        switch ($this->command) {
            case 'analyze':
                $this->runAnalysis();
                break;
            case 'fix':
                $this->runFix();
                break;
            case 'both':
                $this->runBoth();
                break;
            case 'help':
            default:
                $this->showHelp();
                break;
        }
    }

    private function runAnalysis()
    {
        echo "=== GIFT CARD DISCREPANCY ANALYSIS ===\n\n";
        
        try {
            $analyzer = new GiftCardDiscrepancyAnalyzer();
            $discrepancies = $analyzer->analyze();
            
            if (empty($discrepancies)) {
                echo "✅ No discrepancies found! All transactions are in sync.\n";
            } else {
                echo "⚠️  Found " . count($discrepancies) . " discrepancies that need attention.\n";
                echo "📄 Detailed report has been saved to CSV file.\n";
                echo "\nTo fix these discrepancies, run:\n";
                echo "  php gift_card_discrepancy_manager.php fix --dry-run\n";
                echo "  php gift_card_discrepancy_manager.php fix --live\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Error during analysis: " . $e->getMessage() . "\n";
            exit(1);
        }
    }

    private function runFix()
    {
        $dryRun = !in_array('--live', $this->options);
        
        echo "=== GIFT CARD DISCREPANCY FIX ===\n\n";
        
        if (!$dryRun) {
            echo "⚠️  WARNING: Running in LIVE mode!\n";
            echo "This will make actual API calls to the POS system.\n";
            echo "Are you sure you want to continue? (y/N): ";
            
            $handle = fopen("php://stdin", "r");
            $line = fgets($handle);
            fclose($handle);
            
            if (trim(strtolower($line)) !== 'y') {
                echo "Operation cancelled.\n";
                exit(0);
            }
        }
        
        try {
            $fixer = new GiftCardDiscrepancyFixer($dryRun);
            $results = $fixer->fix();
            
            $successful = array_filter($results, function($result) {
                return $result['success'];
            });
            
            if ($dryRun) {
                echo "\n✅ Dry run completed successfully.\n";
                echo "📊 " . count($successful) . " discrepancies would be fixed.\n";
                echo "💡 Run with --live to execute actual fixes.\n";
            } else {
                echo "\n✅ Fix process completed.\n";
                echo "📊 " . count($successful) . " discrepancies were successfully fixed.\n";
                echo "📄 Detailed results saved to CSV file.\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Error during fix: " . $e->getMessage() . "\n";
            exit(1);
        }
    }

    private function runBoth()
    {
        echo "=== GIFT CARD DISCREPANCY ANALYSIS & FIX ===\n\n";
        
        // First run analysis
        try {
            $analyzer = new GiftCardDiscrepancyAnalyzer();
            $discrepancies = $analyzer->analyze();
            
            if (empty($discrepancies)) {
                echo "✅ No discrepancies found! All transactions are in sync.\n";
                return;
            }
            
            echo "\n" . str_repeat("=", 50) . "\n\n";
            
            // Then run fix with the found discrepancies
            $dryRun = !in_array('--live', $this->options);
            
            if (!$dryRun) {
                echo "⚠️  WARNING: About to fix " . count($discrepancies) . " discrepancies in LIVE mode!\n";
                echo "This will make actual API calls to the POS system.\n";
                echo "Are you sure you want to continue? (y/N): ";
                
                $handle = fopen("php://stdin", "r");
                $line = fgets($handle);
                fclose($handle);
                
                if (trim(strtolower($line)) !== 'y') {
                    echo "Fix operation cancelled. Analysis results are still available.\n";
                    return;
                }
            }
            
            $fixer = new GiftCardDiscrepancyFixer($dryRun);
            $results = $fixer->fix($discrepancies);
            
            $successful = array_filter($results, function($result) {
                return $result['success'];
            });
            
            echo "\n✅ Complete process finished.\n";
            echo "📊 Analysis found " . count($discrepancies) . " discrepancies.\n";
            echo "🔧 " . count($successful) . " discrepancies " . ($dryRun ? "would be" : "were") . " fixed.\n";
            
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
            exit(1);
        }
    }

    private function showHelp()
    {
        echo "Gift Card Discrepancy Manager\n";
        echo "=============================\n\n";
        echo "This tool helps identify and fix discrepancies between POS system\n";
        echo "and database gift card transactions.\n\n";
        echo "USAGE:\n";
        echo "  php gift_card_discrepancy_manager.php <command> [options]\n\n";
        echo "COMMANDS:\n";
        echo "  analyze     Analyze discrepancies between POS and database\n";
        echo "  fix         Fix identified discrepancies\n";
        echo "  both        Run analysis and fix in sequence\n";
        echo "  help        Show this help message\n\n";
        echo "OPTIONS:\n";
        echo "  --dry-run   Simulate fixes without making actual POS calls (default)\n";
        echo "  --live      Make actual POS API calls to fix discrepancies\n\n";
        echo "EXAMPLES:\n";
        echo "  # Analyze discrepancies only\n";
        echo "  php gift_card_discrepancy_manager.php analyze\n\n";
        echo "  # Test fixes without making actual changes\n";
        echo "  php gift_card_discrepancy_manager.php fix --dry-run\n\n";
        echo "  # Actually fix the discrepancies\n";
        echo "  php gift_card_discrepancy_manager.php fix --live\n\n";
        echo "  # Analyze and fix in one go (dry run)\n";
        echo "  php gift_card_discrepancy_manager.php both --dry-run\n\n";
        echo "SAFETY:\n";
        echo "  - Always run with --dry-run first to see what would be changed\n";
        echo "  - The --live option requires confirmation before proceeding\n";
        echo "  - All operations generate detailed CSV reports\n\n";
        echo "FILES:\n";
        echo "  - Requires: eichlersapi.csv (POS transaction export)\n";
        echo "  - Generates: gift_card_discrepancy_report_*.csv\n";
        echo "  - Generates: gift_card_fix_report_*.csv (when fixing)\n\n";
    }
}

// Run the manager if script is executed directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $manager = new GiftCardDiscrepancyManager($argv);
    $manager->run();
}
