<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\Http\Controllers\Api\GiftCardController;
use Illuminate\Http\Request;

/**
 * Test script to verify gift card balance retrieval functionality
 * This tests the POS API integration for balance checking
 */

echo "🔍 Testing Gift Card Balance Retrieval...\n\n";

// Test with a sample gift card code from the CSV
$testCodes = [
    'E6137868636084118', // From the CSV sample
    '1000000068119',     // From the CSV sample
    '1000000066841'      // From the CSV sample
];

$controller = new GiftCardController();

foreach ($testCodes as $index => $code) {
    echo "Testing gift card #{$index + 1}: {$code}\n";
    
    try {
        $request = new Request(['code' => $code]);
        $balance = $controller->checkGiftCard($request);
        
        echo "  ✅ Balance retrieved: \$" . number_format($balance, 2) . "\n";
        
    } catch (Exception $e) {
        echo "  ❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "  ---\n";
    
    // Small delay to avoid rate limiting
    sleep(1);
}

echo "\n✅ Balance retrieval test complete.\n";
echo "💡 If errors occurred, check:\n";
echo "   - POS API credentials in .env file\n";
echo "   - Network connectivity\n";
echo "   - Gift card codes are valid in POS system\n";
