<?php

/**
 * Test script to verify the balance status logic
 * This tests the comparison logic for charged amounts vs current balances
 */

echo "🧪 Testing Balance Status Logic...\n\n";

// Test scenarios
$testCases = [
    [
        'description' => 'Sufficient balance - normal case',
        'charged_amount' => 25.00,
        'current_balance' => 50.00,
        'expected_status' => '✅ Sufficient'
    ],
    [
        'description' => 'Exact balance match',
        'charged_amount' => 25.00,
        'current_balance' => 25.00,
        'expected_status' => '✅ Sufficient'
    ],
    [
        'description' => 'Insufficient balance',
        'charged_amount' => 75.00,
        'current_balance' => 50.00,
        'expected_status' => '⚠️ Insufficient'
    ],
    [
        'description' => 'Zero balance',
        'charged_amount' => 10.00,
        'current_balance' => 0.00,
        'expected_status' => '⚠️ Insufficient'
    ],
    [
        'description' => 'Error retrieving balance',
        'charged_amount' => 25.00,
        'current_balance' => 'Error',
        'expected_status' => '❓ Unknown'
    ],
    [
        'description' => 'Non-numeric balance',
        'charged_amount' => 25.00,
        'current_balance' => 'N/A',
        'expected_status' => '❓ Unknown'
    ]
];

function getBalanceStatus($chargedAmount, $currentBalance)
{
    if ($currentBalance !== 'Error' && is_numeric($currentBalance)) {
        if ($currentBalance >= $chargedAmount) {
            return '✅ Sufficient';
        } else {
            return '⚠️ Insufficient';
        }
    } else {
        return '❓ Unknown';
    }
}

$passed = 0;
$failed = 0;

foreach ($testCases as $index => $testCase) {
    echo "Test " . ($index + 1) . ": {$testCase['description']}\n";
    echo "  Charged: \${$testCase['charged_amount']}\n";
    echo "  Balance: " . (is_numeric($testCase['current_balance']) ? '$' . $testCase['current_balance'] : $testCase['current_balance']) . "\n";
    
    $actualStatus = getBalanceStatus($testCase['charged_amount'], $testCase['current_balance']);
    
    if ($actualStatus === $testCase['expected_status']) {
        echo "  ✅ PASS - Status: {$actualStatus}\n";
        $passed++;
    } else {
        echo "  ❌ FAIL - Expected: {$testCase['expected_status']}, Got: {$actualStatus}\n";
        $failed++;
    }
    
    echo "  ---\n";
}

echo "\n📊 Test Results:\n";
echo "  Passed: {$passed}\n";
echo "  Failed: {$failed}\n";
echo "  Total: " . ($passed + $failed) . "\n";

if ($failed === 0) {
    echo "\n🎉 All tests passed! Balance status logic is working correctly.\n";
} else {
    echo "\n⚠️ Some tests failed. Please review the logic.\n";
}

echo "\n💡 This logic will be used in the generateFixReport method to:\n";
echo "  - Compare charged amounts with current POS balances\n";
echo "  - Flag potential data inconsistencies\n";
echo "  - Help identify processing errors\n";
echo "  - Provide actionable insights for manual review\n";
