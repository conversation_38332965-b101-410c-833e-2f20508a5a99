<?php

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/analyze_gift_card_discrepancies.php';

use Illuminate\Support\Facades\DB;
use App\GiftCard;
use App\GiftCardTransaction;
use App\Order;
use App\Http\Controllers\Api\GiftCardController;

/**
 * Gift Card Transaction Discrepancy Fix Script
 * 
 * This script fixes discrepancies by re-sending correct transactions to POS
 * using the existing GiftCardController::voucherPayment() method pattern.
 */

class GiftCardDiscrepancyFixer
{
    private $discrepancies = [];
    private $fixResults = [];
    private $dryRun = true;

    public function __construct($dryRun = true)
    {
        $this->dryRun = $dryRun;
    }

    /**
     * Main fix method
     */
    public function fix($discrepancies = null)
    {
        echo "Starting Gift Card Transaction Discrepancy Fix...\n";
        echo "Mode: " . ($this->dryRun ? "DRY RUN" : "LIVE FIX") . "\n\n";

        // Get discrepancies if not provided
        if ($discrepancies === null) {
            $analyzer = new GiftCardDiscrepancyAnalyzer();
            $this->discrepancies = $analyzer->analyze();
        } else {
            $this->discrepancies = $discrepancies;
        }

        if (empty($this->discrepancies)) {
            echo "No discrepancies to fix.\n";
            return [];
        }

        echo "Processing " . count($this->discrepancies) . " discrepancies...\n\n";

        foreach ($this->discrepancies as $index => $discrepancy) {
            echo "Processing discrepancy #" . ($index + 1) . "...\n";
            $this->processDiscrepancy($discrepancy, $index + 1);
            echo "\n";
        }

        $this->generateFixReport();
        return $this->fixResults;
    }

    /**
     * Process individual discrepancy
     */
    private function processDiscrepancy($discrepancy, $number)
    {
        try {
            // Load the gift card and transaction
            $giftCard = GiftCard::where('code', $discrepancy['gift_card_code'])->first();
            $transaction = GiftCardTransaction::find($discrepancy['transaction_id']);
            $order = Order::find($discrepancy['order_id']);

            if (!$giftCard) {
                throw new Exception("Gift card not found: {$discrepancy['gift_card_code']}");
            }

            if (!$transaction) {
                throw new Exception("Transaction not found: {$discrepancy['transaction_id']}");
            }

            if (!$order) {
                throw new Exception("Order not found: {$discrepancy['order_id']}");
            }

            echo "  Gift Card: {$giftCard->code}\n";
            echo "  Order: {$order->id}\n";
            echo "  Amount to charge: \${$discrepancy['expected_amount']}\n";

            if ($this->dryRun) {
                echo "  [DRY RUN] Would send POS payment request\n";
                $result = [
                    'status' => 'dry_run',
                    'message' => 'Dry run - no actual POS call made',
                    'would_charge' => $discrepancy['expected_amount']
                ];
            } else {
                // Make the actual POS call
                echo "  Sending POS payment request...\n";
                $result = $this->sendPosPayment($giftCard, $transaction, $order, $discrepancy['expected_amount']);
            }

            $this->fixResults[] = [
                'discrepancy_number' => $number,
                'gift_card_code' => $discrepancy['gift_card_code'],
                'order_id' => $discrepancy['order_id'],
                'transaction_id' => $discrepancy['transaction_id'],
                'amount' => $discrepancy['expected_amount'],
                'result' => $result,
                'success' => $result['status'] === 'success' || $result['status'] === 'dry_run'
            ];

            echo "  Result: {$result['status']} - {$result['message']}\n";

        } catch (Exception $e) {
            echo "  ERROR: {$e->getMessage()}\n";
            
            $this->fixResults[] = [
                'discrepancy_number' => $number,
                'gift_card_code' => $discrepancy['gift_card_code'] ?? 'unknown',
                'order_id' => $discrepancy['order_id'] ?? 'unknown',
                'transaction_id' => $discrepancy['transaction_id'] ?? 'unknown',
                'amount' => $discrepancy['expected_amount'] ?? 0,
                'result' => [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ],
                'success' => false
            ];
        }
    }

    /**
     * Send payment to POS system using existing controller method pattern
     */
    private function sendPosPayment($giftCard, $transaction, $order, $amount)
    {
        try {
            // Create a new payment number to avoid conflicts
            $paymentNumber = substr($giftCard->code, -4) . '_FIX_' . substr($order->id, -4) . '_' . time();

            // Use the existing voucherPayment method pattern but with custom implementation
            // to avoid conflicts with the original transaction
            $client = new \GuzzleHttp\Client;

            $json = [
                'VoucherNumber' => $giftCard->code,
                'ChargeAmount' => $amount,
                'SiteId' => 1,
                'PaymentNumber' => $paymentNumber,
                'TenderName' => 'Voucher',
            ];

            $response = $client->post("https://api5.firstchoicepos.com/v1/Voucher/UseForPayment", [
                'headers' => ['Authorization' => 'Basic ' . env('POS')],
                'json' => $json
            ]);

            $responseBody = $response->getBody()->getContents();

            // Update the order meta to track the fix
            $order->update([
                "meta->pos_gift_card_fix->{$giftCard->code}" => [
                    'response' => $responseBody,
                    'json' => $json,
                    'fixed_at' => now(),
                    'original_transaction_id' => $transaction->id
                ]
            ]);

            // Create a new transaction record for the fix
            GiftCardTransaction::create([
                'order_id' => $order->id,
                'gift_card_id' => $giftCard->id,
                'amount' => $amount,
                'type' => 'Fix',
                'pos_message' => [
                    'message' => $responseBody,
                    'json' => $json,
                    'original_transaction_id' => $transaction->id
                ]
            ]);

            return [
                'status' => 'success',
                'message' => 'POS payment sent successfully',
                'response' => $responseBody,
                'payment_number' => $paymentNumber
            ];

        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            if ($e->hasResponse()) {
                $errorResponse = $e->getResponse()->getBody()->getContents();
                $errorMessage .= " - Response: " . $errorResponse;
            }

            return [
                'status' => 'error',
                'message' => $errorMessage
            ];
        }
    }

    /**
     * Generate fix results report
     */
    private function generateFixReport()
    {
        echo "\n=== FIX RESULTS REPORT ===\n\n";

        $successful = array_filter($this->fixResults, function($result) {
            return $result['success'];
        });

        $failed = array_filter($this->fixResults, function($result) {
            return !$result['success'];
        });

        echo "Total processed: " . count($this->fixResults) . "\n";
        echo "Successful: " . count($successful) . "\n";
        echo "Failed: " . count($failed) . "\n\n";

        if (!empty($failed)) {
            echo "FAILED FIXES:\n";
            foreach ($failed as $failure) {
                echo "  #{$failure['discrepancy_number']}: {$failure['gift_card_code']} (Order: {$failure['order_id']}) - {$failure['result']['message']}\n";
            }
            echo "\n";
        }

        $totalAmount = array_sum(array_column($successful, 'amount'));
        echo "Total amount " . ($this->dryRun ? "would be" : "was") . " charged: \${$totalAmount}\n";

        // Save detailed report
        $this->saveFixReportToFile();
    }

    /**
     * Save fix report to file
     */
    private function saveFixReportToFile()
    {
        $filename = 'gift_card_fix_report_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = __DIR__ . '/' . $filename;
        
        $handle = fopen($filepath, 'w');
        
        // Write header
        fputcsv($handle, [
            'Discrepancy Number',
            'Gift Card Code',
            'Order ID',
            'Transaction ID',
            'Amount',
            'Status',
            'Message',
            'Success'
        ]);
        
        // Write data
        foreach ($this->fixResults as $result) {
            fputcsv($handle, [
                $result['discrepancy_number'],
                $result['gift_card_code'],
                $result['order_id'],
                $result['transaction_id'],
                $result['amount'],
                $result['result']['status'],
                $result['result']['message'],
                $result['success'] ? 'Yes' : 'No'
            ]);
        }
        
        fclose($handle);
        echo "Fix report saved to: {$filepath}\n";
    }

    /**
     * Set dry run mode
     */
    public function setDryRun($dryRun)
    {
        $this->dryRun = $dryRun;
    }
}

// Run the fix if script is executed directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $dryRun = true; // Default to dry run for safety
    
    // Check command line arguments
    if (isset($argv[1]) && $argv[1] === '--live') {
        $dryRun = false;
        echo "WARNING: Running in LIVE mode. This will make actual POS API calls!\n";
        echo "Press Enter to continue or Ctrl+C to cancel...\n";
        fgets(STDIN);
    }
    
    try {
        $fixer = new GiftCardDiscrepancyFixer($dryRun);
        $results = $fixer->fix();
        
        echo "\nFix process complete.\n";
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}
